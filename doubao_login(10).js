/**
 * 豆包WebView自动登录模块
 * 支持手机号+短信验证码登录
 */

var utils = require(files.path("./utils.js"));
var SmsReader = require(files.path("./sms_reader.js")).SmsReader;

/**
 * 豆包登录管理器
 */
function DoubaoLoginManager() {
    this.webview = null;
    this.webviewControl = null;
    this.smsReader = null;
}

/**
 * 初始化WebView
 */
DoubaoLoginManager.prototype.init = function (webviewControl) {
    this.webviewControl = webviewControl;

    // 初始化短信读取器
    this.smsReader = new SmsReader();
    if (!this.smsReader.requestSmsPermission()) {
        utils.log("DOUBAO_LOGIN: 短信权限提醒完成，将使用手动输入模式", "warn");
    }

    utils.log("DOUBAO_LOGIN: 登录管理器初始化完成");
    return true;
};

/**
 * 检测当前登录状态
 */
DoubaoLoginManager.prototype.checkLoginStatus = function () {
    utils.log("DOUBAO_LOGIN: 检测登录状态...");

    var isLoggedIn = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 检测登录状态的多种方法
                var indicators = {
                    hasLoginButton: false,
                    hasUserAvatar: false,
                    hasWelcomeText: false,
                    pageContent: ''
                };
                
                // 方法1: 查找登录按钮（使用精确选择器）
                var specificLoginButton = document.querySelector('button[data-testid="to_login_button"]');
                if (specificLoginButton) {
                    indicators.hasLoginButton = true;
                } else {
                    // 备用方法：查找包含登录文字的按钮
                    var loginButtons = document.querySelectorAll('button, a, div');
                    for (var i = 0; i < loginButtons.length; i++) {
                        var text = loginButtons[i].textContent || loginButtons[i].innerText || '';
                        if (text.includes('登录') || text.includes('注册') || text.includes('手机号')) {
                            indicators.hasLoginButton = true;
                            break;
                        }
                    }
                }
                
                // 方法2: 查找用户头像或用户信息
                var userElements = document.querySelectorAll('[class*="avatar"], [class*="user"], [class*="profile"]');
                indicators.hasUserAvatar = userElements.length > 0;
                
                // 方法3: 检查欢迎文字
                indicators.hasWelcomeText = document.body.textContent.includes('你好，我是豆包');
                
                // 方法4: 获取页面主要内容用于判断
                indicators.pageContent = document.body.textContent.substring(0, 200);
                
                return indicators;
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                try {
                    var result = value ? JSON.parse(value.replace(/^"|"$/g, '')) : null;
                    if (result) {
                        utils.log("DOUBAO_LOGIN: 登录状态检测结果:");
                        utils.log("- 有登录按钮: " + result.hasLoginButton);
                        utils.log("- 有用户头像: " + result.hasUserAvatar);
                        utils.log("- 有欢迎文字: " + result.hasWelcomeText);
                        utils.log("- 页面内容: " + result.pageContent.substring(0, 100) + "...");

                        // 判断登录状态：
                        // 1. 如果有欢迎文字且没有登录按钮，说明已登录
                        // 2. 如果页面内容包含聊天相关内容且没有登录按钮，也说明已登录
                        var hasChat = result.pageContent.includes('发消息') ||
                            result.pageContent.includes('输入') ||
                            result.pageContent.includes('聊天');

                        isLoggedIn = (result.hasWelcomeText || hasChat) && !result.hasLoginButton;
                    }
                } catch (e) {
                    utils.log("DOUBAO_LOGIN: 解析登录状态失败: " + e);
                }
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    utils.log("DOUBAO_LOGIN: 当前登录状态: " + (isLoggedIn ? "已登录" : "未登录"));
    return isLoggedIn;
};

/**
 * 查找并点击登录按钮
 */
DoubaoLoginManager.prototype.clickLoginButton = function () {
    utils.log("DOUBAO_LOGIN: 查找登录按钮...");

    var loginClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 使用正确的登录按钮选择器
                var loginButton = document.querySelector('button[data-testid="to_login_button"]');

                if (loginButton) {
                    // 高亮登录按钮
                    loginButton.style.border = '3px solid red';
                    loginButton.style.boxShadow = '0 0 10px red';

                    // 点击登录按钮
                    loginButton.click();

                    // 也尝试触发其他事件
                    var clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    loginButton.dispatchEvent(clickEvent);

                    return '找到并点击登录按钮: ' + (loginButton.textContent || loginButton.innerText);
                } else {
                    // 备用方法：查找包含"登录"文字的按钮
                    var buttons = document.querySelectorAll('button');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var text = btn.textContent || btn.innerText || '';
                        if (text.includes('登录')) {
                            btn.style.border = '3px solid red';
                            btn.style.boxShadow = '0 0 10px red';
                            btn.click();
                            return '备用方法找到并点击登录按钮: ' + text;
                        }
                    }
                    return '未找到登录按钮';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                loginClicked = result.includes('找到并点击');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    if (loginClicked) {
        utils.log("DOUBAO_LOGIN: 登录按钮点击成功，等待登录页面加载...");
        sleep(3000); // 等待登录页面加载
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 未找到或点击登录按钮失败");
        return false;
    }
};

/**
 * 输入手机号
 */
DoubaoLoginManager.prototype.inputPhoneNumber = function (phoneNumber) {
    utils.log("DOUBAO_LOGIN: 输入手机号: " + phoneNumber);

    var inputSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                var phone = '${phoneNumber}';

                // 使用正确的手机号输入框选择器
                var phoneInput = document.querySelector('input[data-testid="login_phone_number_input"]');

                if (phoneInput) {
                    // 高亮输入框
                    phoneInput.style.border = '3px solid blue';
                    phoneInput.style.backgroundColor = 'lightyellow';

                    // 清空并输入手机号
                    phoneInput.focus();
                    phoneInput.value = '';

                    // 使用原生setter
                    var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(phoneInput, phone);

                    // 触发事件
                    phoneInput.dispatchEvent(new Event('input', { bubbles: true }));
                    phoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                    phoneInput.dispatchEvent(new Event('blur', { bubbles: true }));

                    return '手机号输入成功: ' + phoneInput.value;
                } else {
                    // 备用方法：查找包含"手机号"的输入框
                    var inputs = document.querySelectorAll('input');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        var placeholder = input.placeholder || '';
                        if (placeholder.includes('手机号') || placeholder.includes('请输入手机号')) {
                            input.style.border = '3px solid blue';
                            input.style.backgroundColor = 'lightyellow';
                            input.focus();
                            input.value = phone;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            return '备用方法手机号输入成功: ' + input.value;
                        }
                    }
                    return '未找到手机号输入框';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                inputSuccess = result.includes('输入成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    return inputSuccess;
};

/**
 * 勾选同意协议
 */
DoubaoLoginManager.prototype.checkAgreement = function () {
    utils.log("DOUBAO_LOGIN: 勾选同意协议...");

    var checkSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 查找同意协议的复选框
                var checkbox = document.querySelector('input[type="checkbox"]');

                if (checkbox) {
                    // 高亮复选框
                    var checkboxContainer = checkbox.parentElement;
                    if (checkboxContainer) {
                        checkboxContainer.style.border = '3px solid green';
                        checkboxContainer.style.backgroundColor = 'lightgreen';
                    }

                    // 如果未勾选，则勾选
                    if (!checkbox.checked) {
                        checkbox.click();

                        // 也尝试触发其他事件
                        var changeEvent = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(changeEvent);
                    }

                    return '同意协议勾选成功，状态: ' + checkbox.checked;
                } else {
                    return '未找到同意协议复选框';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                checkSuccess = result.includes('勾选成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    return checkSuccess;
};

/**
 * 点击下一步按钮
 */
DoubaoLoginManager.prototype.clickNextButton = function () {
    utils.log("DOUBAO_LOGIN: 点击下一步按钮...");

    var nextButtonClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 使用正确的下一步按钮选择器
                var nextButton = document.querySelector('button[data-testid="login_next_button"]');

                if (nextButton) {
                    // 高亮按钮
                    nextButton.style.border = '3px solid green';
                    nextButton.style.backgroundColor = 'lightgreen';

                    // 点击按钮
                    nextButton.click();

                    var clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    nextButton.dispatchEvent(clickEvent);

                    return '下一步按钮点击成功';
                } else {
                    // 备用方法：查找包含"下一步"的按钮
                    var buttons = document.querySelectorAll('button');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var text = btn.textContent || btn.innerText || '';
                        if (text.includes('下一步')) {
                            btn.style.border = '3px solid green';
                            btn.style.backgroundColor = 'lightgreen';
                            btn.click();
                            return '备用方法下一步按钮点击成功: ' + text;
                        }
                    }
                    return '未找到下一步按钮';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                nextButtonClicked = result.includes('点击成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    if (nextButtonClicked) {
        utils.log("DOUBAO_LOGIN: 下一步按钮点击成功，等待验证码页面加载...");
        sleep(3000); // 等待验证码页面加载
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 下一步按钮点击失败");
        return false;
    }
};

/**
 * 获取短信验证码（支持用户选择输入方式）
 */
DoubaoLoginManager.prototype.waitForVerificationCode = function (preferManual) {
    utils.log("DOUBAO_LOGIN: 开始获取短信验证码...");
    utils.log("DOUBAO_LOGIN: 用户偏好设置 - 优先手动输入: " + (preferManual ? "是" : "否"));

    // 如果用户选择优先手动输入，直接使用手动输入
    if (preferManual) {
        utils.log("DOUBAO_LOGIN: 用户选择优先手动输入验证码");
        return this.waitForVerificationCodeManual();
    }

    // 否则尝试自动获取，失败时回退到手动输入
    if (!this.smsReader) {
        utils.log("DOUBAO_LOGIN: 短信读取器未初始化，使用备用手动输入", "warn");
        return this.waitForVerificationCodeManual();
    }

    // 使用自动短信读取器
    utils.log("DOUBAO_LOGIN: 尝试自动获取短信验证码...");
    var code = this.smsReader.waitForVerificationCode(120); // 等待2分钟

    if (code) {
        utils.log("DOUBAO_LOGIN: 自动获取到验证码: " + code);
        return code;
    } else {
        utils.log("DOUBAO_LOGIN: 自动获取验证码失败，切换到手动输入", "warn");
        return this.waitForVerificationCodeManual();
    }
};

/**
 * 手动输入验证码（备用方案）
 */
DoubaoLoginManager.prototype.waitForVerificationCodeManual = function () {
    utils.log("DOUBAO_LOGIN: 等待用户手动输入验证码...");

    // 使用同步的输入对话框
    var code = null;

    try {
        // 使用同步方式获取用户输入
        code = dialogs.rawInput("请输入收到的短信验证码:");

        if (code && code.trim()) {
            code = code.trim();
            utils.log("DOUBAO_LOGIN: 用户输入验证码: " + code);
        } else {
            utils.log("DOUBAO_LOGIN: 用户取消输入验证码");
            code = null;
        }
    } catch (e) {
        utils.log("DOUBAO_LOGIN: 验证码输入异常: " + e);
        code = null;
    }

    if (code) {
        utils.log("DOUBAO_LOGIN: 获取到验证码: " + code);
        return code;
    } else {
        utils.log("DOUBAO_LOGIN: 验证码输入超时或取消");
        return null;
    }
};

/**
 * 尝试使用Auto.js原生输入方法
 */
DoubaoLoginManager.prototype.tryNativeInput = function (code) {
    utils.log("DOUBAO_LOGIN: 尝试Auto.js原生输入方法");

    var self = this;
    var inputSuccess = false;

    try {
        // 第一步：聚焦到验证码输入框
        utils.log("DOUBAO_LOGIN: 聚焦验证码输入框...");
        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    var codeInput = null;

                    // 查找验证码输入框
                    var codeContainer = document.querySelector('div[data-testid="code_input"]');
                    if (codeContainer) {
                        codeInput = codeContainer.querySelector('input[type="text"]') ||
                                   codeContainer.querySelector('input') ||
                                   codeContainer.querySelector('input[inputmode="decimal"]');
                    }

                    if (!codeInput) {
                        var inputs = document.querySelectorAll('input');
                        for (var i = 0; i < inputs.length; i++) {
                            var input = inputs[i];
                            var inputMode = (input.inputMode || '').toLowerCase();
                            if (inputMode === 'decimal' || inputMode === 'numeric') {
                                codeInput = input;
                                break;
                            }
                        }
                    }

                    if (codeInput) {
                        // 高亮输入框
                        codeInput.style.border = '5px solid red';
                        codeInput.style.backgroundColor = '#ffcccc';

                        // 聚焦并选中
                        codeInput.focus();
                        codeInput.click();
                        codeInput.select();

                        return 'input_focused_success';
                    } else {
                        return 'input_not_found';
                    }
                })();
            `, null);
        });

        sleep(1000);

        // 第二步：清空现有内容
        utils.log("DOUBAO_LOGIN: 清空现有内容...");
        for (let i = 0; i < 10; i++) {
            ui.run(function () {
                self.webviewControl.dispatchKeyEvent(new android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, android.view.KeyEvent.KEYCODE_DEL));
                self.webviewControl.dispatchKeyEvent(new android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, android.view.KeyEvent.KEYCODE_DEL));
            });
            sleep(50);
        }

        sleep(500);

        // 第三步：逐字符输入验证码
        utils.log("DOUBAO_LOGIN: 开始输入验证码: " + code);
        for (let i = 0; i < code.length; i++) {
            let char = code.charAt(i);
            let keyCode = android.view.KeyEvent.KEYCODE_0 + parseInt(char);

            utils.log("DOUBAO_LOGIN: 输入字符: " + char + " (KeyCode: " + keyCode + ")");

            ui.run(function () {
                self.webviewControl.dispatchKeyEvent(new android.view.KeyEvent(android.view.KeyEvent.ACTION_DOWN, keyCode));
                self.webviewControl.dispatchKeyEvent(new android.view.KeyEvent(android.view.KeyEvent.ACTION_UP, keyCode));
            });

            sleep(200); // 增加间隔时间
        }

        sleep(1000);

        // 第四步：验证输入是否成功
        utils.log("DOUBAO_LOGIN: 验证原生输入结果...");
        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    var codeInput = null;

                    var codeContainer = document.querySelector('div[data-testid="code_input"]');
                    if (codeContainer) {
                        codeInput = codeContainer.querySelector('input[type="text"]') ||
                                   codeContainer.querySelector('input') ||
                                   codeContainer.querySelector('input[inputmode="decimal"]');
                    }

                    if (!codeInput) {
                        var inputs = document.querySelectorAll('input');
                        for (var i = 0; i < inputs.length; i++) {
                            var input = inputs[i];
                            var inputMode = (input.inputMode || '').toLowerCase();
                            if (inputMode === 'decimal' || inputMode === 'numeric') {
                                codeInput = input;
                                break;
                            }
                        }
                    }

                    if (codeInput) {
                        var actualValue = codeInput.value || '';
                        var expectedValue = '${code}';

                        return 'native_input_result|期望: ' + expectedValue + ', 实际: ' + actualValue + ', 匹配: ' + (actualValue === expectedValue);
                    } else {
                        return 'native_input_result|输入框未找到';
                    }
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    var result = value ? value.replace(/^"|"$/g, '') : '';
                    utils.log("DOUBAO_LOGIN: 原生输入验证结果: " + result);
                    inputSuccess = result.includes('匹配: true');
                }
            }));
        });

        sleep(2000);

        if (inputSuccess) {
            utils.log("DOUBAO_LOGIN: ✓ Auto.js原生输入成功");
            return true;
        } else {
            utils.log("DOUBAO_LOGIN: ✗ Auto.js原生输入失败");
            return false;
        }

    } catch (e) {
        utils.log("DOUBAO_LOGIN: Auto.js原生输入异常: " + e);
        return false;
    }
};

/**
 * 输入验证码
 */
DoubaoLoginManager.prototype.inputVerificationCode = function (code) {
    utils.log("DOUBAO_LOGIN: 输入验证码: " + code);

    // 首先尝试Auto.js原生输入方法
    if (this.tryNativeInput(code)) {
        return true;
    }

    // 如果原生输入失败，使用JavaScript方法
    utils.log("DOUBAO_LOGIN: 原生输入失败，尝试JavaScript方法");

    var inputSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                var verifyCode = '${code}';
                console.log('开始输入验证码: ' + verifyCode);

                // 多种方法查找验证码输入框
                var codeInput = null;
                var foundMethod = '';

                // 方法1: 查找data-testid="code_input"容器
                var codeContainer = document.querySelector('div[data-testid="code_input"]');
                if (codeContainer) {
                    codeInput = codeContainer.querySelector('input[type="text"]') ||
                               codeContainer.querySelector('input') ||
                               codeContainer.querySelector('input[inputmode="decimal"]');
                    if (codeInput) foundMethod = '方法1: data-testid容器';
                }

                // 方法2: 直接查找验证码相关的输入框
                if (!codeInput) {
                    var inputs = document.querySelectorAll('input');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        var placeholder = (input.placeholder || '').toLowerCase();
                        var inputMode = (input.inputMode || '').toLowerCase();
                        var type = (input.type || '').toLowerCase();
                        var name = (input.name || '').toLowerCase();
                        var id = (input.id || '').toLowerCase();

                        // 检查是否是验证码输入框
                        if (inputMode === 'decimal' ||
                            inputMode === 'numeric' ||
                            placeholder.includes('验证码') ||
                            placeholder.includes('code') ||
                            placeholder.includes('验证') ||
                            name.includes('code') ||
                            name.includes('verify') ||
                            id.includes('code') ||
                            id.includes('verify') ||
                            (type === 'text' && input.maxLength >= 4 && input.maxLength <= 8)) {
                            codeInput = input;
                            foundMethod = '方法2: 属性匹配 (' + placeholder + ', ' + inputMode + ', ' + type + ')';
                            break;
                        }
                    }
                }

                // 方法3: 查找最近可能的输入框（如果前面都没找到）
                if (!codeInput) {
                    var allInputs = document.querySelectorAll('input[type="text"], input:not([type])');
                    if (allInputs.length > 0) {
                        // 选择最后一个文本输入框（通常验证码输入框在最后）
                        codeInput = allInputs[allInputs.length - 1];
                        foundMethod = '方法3: 最后一个文本输入框';
                    }
                }

                if (codeInput) {
                    var debugInfo = [];
                    debugInfo.push('找到验证码输入框: ' + foundMethod);

                    // 详细记录输入框信息
                    debugInfo.push('输入框详细信息:');
                    debugInfo.push('- tagName: ' + codeInput.tagName);
                    debugInfo.push('- type: ' + codeInput.type);
                    debugInfo.push('- placeholder: ' + codeInput.placeholder);
                    debugInfo.push('- disabled: ' + codeInput.disabled);
                    debugInfo.push('- readOnly: ' + codeInput.readOnly);
                    debugInfo.push('- maxLength: ' + codeInput.maxLength);
                    debugInfo.push('- inputMode: ' + codeInput.inputMode);
                    debugInfo.push('- className: ' + codeInput.className);
                    debugInfo.push('- id: ' + codeInput.id);
                    debugInfo.push('- name: ' + codeInput.name);
                    debugInfo.push('- 初始值: ' + (codeInput.value || ''));

                    // 高亮输入框以便调试
                    codeInput.style.border = '3px solid orange';
                    codeInput.style.backgroundColor = '#ffe6cc';
                    codeInput.style.outline = '2px solid red';

                    // 检查输入框是否可编辑
                    if (codeInput.disabled) {
                        debugInfo.push('⚠️ 输入框被禁用');
                        return '输入框被禁用，无法输入验证码|' + debugInfo.join(';');
                    }

                    if (codeInput.readOnly) {
                        debugInfo.push('⚠️ 输入框为只读');
                        return '输入框为只读，无法输入验证码|' + debugInfo.join(';');
                    }

                    // 聚焦输入框
                    debugInfo.push('正在聚焦输入框...');
                    codeInput.focus();

                    // 清空现有内容
                    debugInfo.push('清空现有内容...');
                    var initialValue = codeInput.value || '';
                    codeInput.value = '';
                    debugInfo.push('清空后的值: ' + (codeInput.value || '') + ' (之前: ' + initialValue + ')');

                    // 直接同步执行输入操作
                    debugInfo.push('开始输入验证码: ' + verifyCode);

                    // 方法1: 直接设置value
                    debugInfo.push('方法1: 直接设置value');
                    var oldValue = codeInput.value;
                    codeInput.value = verifyCode;
                    debugInfo.push('设置后的值: ' + codeInput.value + ' (之前: ' + oldValue + ')');

                    // 方法2: 使用原生setter
                    debugInfo.push('方法2: 使用原生setter');
                    try {
                        var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                        if (nativeInputValueSetter) {
                            nativeInputValueSetter.call(codeInput, verifyCode);
                            debugInfo.push('原生setter执行完成，当前值: ' + codeInput.value);
                        } else {
                            debugInfo.push('未找到原生setter');
                        }
                    } catch (e) {
                        debugInfo.push('原生setter失败: ' + e);
                    }

                    // 方法3: 触发input事件
                    debugInfo.push('方法3: 触发input事件');
                    try {
                        var inputEvent = new Event('input', { bubbles: true, cancelable: true });
                        codeInput.dispatchEvent(inputEvent);
                        debugInfo.push('input事件触发成功');
                    } catch (e) {
                        debugInfo.push('input事件触发失败: ' + e);
                    }

                    // 最终检查
                    debugInfo.push('最终值: ' + codeInput.value);

                    return '验证码输入操作完成 (' + foundMethod + '): ' + verifyCode + '|DEBUG:' + debugInfo.join(';');
                } else {
                    console.log('未找到任何验证码输入框');

                    // 输出详细的页面信息用于调试
                    var pageInfo = {
                        title: document.title,
                        url: window.location.href,
                        bodyText: document.body.textContent.substring(0, 200),
                        inputCount: document.querySelectorAll('input').length,
                        allElementsCount: document.querySelectorAll('*').length,
                        inputs: [],
                        divs: [],
                        buttons: []
                    };

                    // 详细记录所有输入框
                    var allInputs = document.querySelectorAll('input');
                    for (var i = 0; i < allInputs.length; i++) {
                        var inp = allInputs[i];
                        pageInfo.inputs.push({
                            index: i,
                            type: inp.type,
                            placeholder: inp.placeholder,
                            name: inp.name,
                            id: inp.id,
                            className: inp.className,
                            inputMode: inp.inputMode,
                            maxLength: inp.maxLength,
                            value: inp.value,
                            visible: inp.offsetWidth > 0 && inp.offsetHeight > 0,
                            disabled: inp.disabled,
                            readonly: inp.readOnly
                        });
                    }

                    // 记录可能的验证码相关div
                    var divs = document.querySelectorAll('div[data-testid*="code"], div[class*="code"], div[class*="verify"]');
                    for (var i = 0; i < Math.min(divs.length, 3); i++) {
                        var div = divs[i];
                        pageInfo.divs.push({
                            testid: div.getAttribute('data-testid'),
                            className: div.className,
                            innerHTML: div.innerHTML.substring(0, 100)
                        });
                    }

                    // 记录按钮信息
                    var buttons = document.querySelectorAll('button');
                    for (var i = 0; i < Math.min(buttons.length, 5); i++) {
                        var btn = buttons[i];
                        pageInfo.buttons.push({
                            text: (btn.textContent || '').trim(),
                            className: btn.className,
                            type: btn.type,
                            disabled: btn.disabled
                        });
                    }

                    return '未找到验证码输入框。详细页面信息: ' + JSON.stringify(pageInfo);
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);

                // 不能只看"输入成功"字样，需要进一步验证
                if (result.includes('验证码输入操作完成')) {
                    utils.log("DOUBAO_LOGIN: JavaScript操作完成，需要进一步验证实际输入效果");
                    inputSuccess = false; // 先设为false，等待进一步验证
                } else {
                    inputSuccess = false;
                }
            }
        }));
    });

    // 等待回调执行
    sleep(3000); // 增加等待时间，让输入操作充分完成

    // 进一步验证：检查验证码是否真的输入到了输入框中
    utils.log("DOUBAO_LOGIN: 验证验证码是否真的输入到输入框中...");

    var verificationResult = false;
    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 再次查找验证码输入框并检查其值
                var codeInput = null;

                // 使用相同的查找逻辑
                var codeContainer = document.querySelector('div[data-testid="code_input"]');
                if (codeContainer) {
                    codeInput = codeContainer.querySelector('input[type="text"]') ||
                               codeContainer.querySelector('input') ||
                               codeContainer.querySelector('input[inputmode="decimal"]');
                }

                if (!codeInput) {
                    var inputs = document.querySelectorAll('input');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        var placeholder = (input.placeholder || '').toLowerCase();
                        var inputMode = (input.inputMode || '').toLowerCase();

                        if (inputMode === 'decimal' || inputMode === 'numeric' ||
                            placeholder.includes('验证码') || placeholder.includes('code')) {
                            codeInput = input;
                            break;
                        }
                    }
                }

                if (codeInput) {
                    var actualValue = codeInput.value || codeInput.textContent || '';
                    var expectedValue = '${code}';

                    console.log('验证检查 - 期望值: ' + expectedValue + ', 实际值: ' + actualValue);

                    if (actualValue === expectedValue) {
                        return 'verification_success|验证码已正确输入到输入框: ' + actualValue;
                    } else {
                        return 'verification_failed|验证码输入失败，期望: ' + expectedValue + ', 实际: ' + actualValue;
                    }
                } else {
                    return 'verification_failed|未找到验证码输入框进行验证';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: 验证结果: " + result);
                verificationResult = result.includes('verification_success');
            }
        }));
    });

    // 等待验证完成
    sleep(2000);

    if (verificationResult) {
        utils.log("DOUBAO_LOGIN: ✓ 验证码输入验证成功");
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: ✗ 验证码输入验证失败");
        return false;
    }
};

/**
 * 点击登录/确认按钮
 */
DoubaoLoginManager.prototype.clickConfirmButton = function () {
    utils.log("DOUBAO_LOGIN: 点击登录确认按钮...");

    var confirmClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                console.log('开始查找确认按钮...');

                // 查找登录/确认按钮
                var confirmButton = null;
                var foundMethod = '';

                // 方法1: 查找包含特定文本的按钮
                var textButtons = document.querySelectorAll('button, a, div[role="button"], span[role="button"]');
                for (var i = 0; i < textButtons.length; i++) {
                    var btn = textButtons[i];
                    var text = (btn.textContent || btn.innerText || '').trim();

                    if (text === '登录' || text === '确认' || text === '提交' ||
                        text === '完成' || text === '下一步' || text === '确定' ||
                        text === 'Login' || text === 'Submit' || text === 'Confirm') {
                        confirmButton = btn;
                        foundMethod = '方法1: 文本匹配 (' + text + ')';
                        break;
                    }
                }

                // 方法2: 查找主要的提交按钮（通过class）
                if (!confirmButton) {
                    var classButtons = document.querySelectorAll('button, a, div, span');
                    for (var i = 0; i < classButtons.length; i++) {
                        var btn = classButtons[i];
                        var className = (btn.className || '').toLowerCase();

                        if (className.includes('primary') || className.includes('submit') ||
                            className.includes('confirm') || className.includes('login') ||
                            className.includes('btn-primary') || className.includes('main-btn')) {
                            confirmButton = btn;
                            foundMethod = '方法2: 样式类匹配 (' + className + ')';
                            break;
                        }
                    }
                }

                // 方法3: 查找type="submit"的按钮
                if (!confirmButton) {
                    var submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    if (submitButtons.length > 0) {
                        confirmButton = submitButtons[0];
                        foundMethod = '方法3: submit类型按钮';
                    }
                }

                // 方法4: 查找最可能的按钮（最后的可点击元素）
                if (!confirmButton) {
                    var allClickable = document.querySelectorAll('button:not([disabled]), a[href], div[onclick], span[onclick]');
                    if (allClickable.length > 0) {
                        // 选择最后一个可点击元素
                        confirmButton = allClickable[allClickable.length - 1];
                        foundMethod = '方法4: 最后一个可点击元素';
                    }
                }

                if (confirmButton) {
                    console.log('找到确认按钮: ' + foundMethod);

                    // 高亮按钮以便调试
                    confirmButton.style.border = '3px solid purple';
                    confirmButton.style.backgroundColor = '#e6ccff';
                    confirmButton.style.outline = '2px solid blue';

                    // 确保按钮可见
                    confirmButton.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 等待一下再点击
                    setTimeout(function() {
                        // 多种方式点击按钮
                        try {
                            confirmButton.click();
                        } catch (e) {
                            console.log('直接点击失败: ' + e);
                        }

                        try {
                            var clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            confirmButton.dispatchEvent(clickEvent);
                        } catch (e) {
                            console.log('事件点击失败: ' + e);
                        }

                        // 如果是表单按钮，尝试提交表单
                        try {
                            var form = confirmButton.closest('form');
                            if (form) {
                                form.submit();
                            }
                        } catch (e) {
                            console.log('表单提交失败: ' + e);
                        }

                        console.log('确认按钮点击完成');
                    }, 100);

                    return '登录确认按钮点击成功 (' + foundMethod + ')';
                } else {
                    console.log('未找到任何确认按钮');

                    // 输出页面信息用于调试
                    var pageInfo = {
                        title: document.title,
                        url: window.location.href,
                        buttonCount: document.querySelectorAll('button').length,
                        buttons: []
                    };

                    var allButtons = document.querySelectorAll('button, a, div[role="button"]');
                    for (var i = 0; i < Math.min(allButtons.length, 5); i++) {
                        var btn = allButtons[i];
                        pageInfo.buttons.push({
                            text: (btn.textContent || btn.innerText || '').trim(),
                            className: btn.className,
                            type: btn.type,
                            disabled: btn.disabled
                        });
                    }

                    return '未找到登录确认按钮。页面信息: ' + JSON.stringify(pageInfo);
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                confirmClicked = result.includes('点击成功');
            }
        }));
    });

    // 等待回调执行
    sleep(2000); // 增加等待时间

    if (confirmClicked) {
        utils.log("DOUBAO_LOGIN: 登录确认按钮点击成功，等待登录完成...");
        sleep(3000); // 等待登录处理
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 登录确认按钮点击失败");
        return false;
    }
};

/**
 * 等待登录完成
 */
DoubaoLoginManager.prototype.waitForLoginComplete = function () {
    utils.log("DOUBAO_LOGIN: 等待登录完成...");

    var maxWait = 30; // 最多等待30秒
    var waitCount = 0;
    var loginComplete = false;
    var self = this;

    while (waitCount < maxWait && !loginComplete) {
        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    // 检查登录完成的标志
                    var hasWelcome = document.body.textContent.includes('你好，我是豆包');
                    var hasLoginButton = false;

                    // 检查是否还有登录按钮
                    var buttons = document.querySelectorAll('button, a, div, span');
                    for (var i = 0; i < buttons.length; i++) {
                        var text = buttons[i].textContent || buttons[i].innerText || '';
                        if (text.includes('登录') || text.includes('注册')) {
                            hasLoginButton = true;
                            break;
                        }
                    }

                    // 检查URL变化
                    var currentUrl = window.location.href;

                    // 如果有登录按钮且有欢迎页面，说明登录失败（在聊天页面但未登录）
                    var loginFailed = hasLoginButton && hasWelcome;

                    return {
                        hasWelcome: hasWelcome,
                        hasLoginButton: hasLoginButton,
                        url: currentUrl,
                        loginComplete: hasWelcome && !hasLoginButton && !loginFailed,
                        loginFailed: loginFailed
                    };
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    try {
                        var result = value ? JSON.parse(value.replace(/^"|"$/g, '')) : null;
                        if (result) {
                            utils.log("DOUBAO_LOGIN: 登录状态检查 - 欢迎页面: " + result.hasWelcome +
                                ", 登录按钮: " + result.hasLoginButton + ", URL: " + result.url);

                            if (result.loginFailed) {
                                utils.log("DOUBAO_LOGIN: 检测到登录失败，停止等待");
                                loginComplete = false;
                                waitCount = maxWait; // 强制退出循环
                            } else if (result.loginComplete) {
                                loginComplete = true;
                                utils.log("DOUBAO_LOGIN: 登录完成！");
                            }
                        }
                    } catch (e) {
                        utils.log("DOUBAO_LOGIN: 解析登录状态失败: " + e);
                    }
                }
            }));
        });

        sleep(1000);
        waitCount++;
    }

    if (loginComplete) {
        utils.log("DOUBAO_LOGIN: 登录成功完成");
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 登录完成检测超时");
        return false;
    }
};

/**
 * 完整的自动登录流程
 */
DoubaoLoginManager.prototype.autoLogin = function (phoneNumber, preferManualCode) {
    utils.log("DOUBAO_LOGIN: 开始自动登录流程，手机号: " + phoneNumber);
    utils.log("DOUBAO_LOGIN: 验证码输入方式 - 优先手动输入: " + (preferManualCode ? "是" : "否"));

    try {
        // 1. 检查当前登录状态
        if (this.checkLoginStatus()) {
            utils.log("DOUBAO_LOGIN: 已经登录，无需重复登录");
            return { success: true, message: "已经登录" };
        }

        // 2. 点击登录按钮
        if (!this.clickLoginButton()) {
            return { success: false, message: "点击登录按钮失败" };
        }

        // 3. 输入手机号
        if (!this.inputPhoneNumber(phoneNumber)) {
            return { success: false, message: "输入手机号失败" };
        }

        // 4. 勾选同意协议
        if (!this.checkAgreement()) {
            utils.log("DOUBAO_LOGIN: 勾选协议失败，但继续流程...");
        }

        // 5. 点击下一步按钮
        if (!this.clickNextButton()) {
            return { success: false, message: "点击下一步按钮失败" };
        }

        // 6. 等待用户输入验证码（根据用户偏好选择输入方式）
        var code = this.waitForVerificationCode(preferManualCode);
        if (!code) {
            return { success: false, message: "验证码输入超时或取消" };
        }

        // 7. 输入验证码
        if (!this.inputVerificationCode(code)) {
            return { success: false, message: "输入验证码失败" };
        }

        // 8. 尝试点击确认按钮（有些情况下需要手动点击）
        utils.log("DOUBAO_LOGIN: 验证码输入完成，尝试点击确认按钮...");
        sleep(1000); // 等待验证码输入完成

        if (!this.clickConfirmButton()) {
            utils.log("DOUBAO_LOGIN: 确认按钮点击失败，但继续等待登录完成...");
        }

        // 9. 等待登录完成
        if (!this.waitForLoginComplete()) {
            return { success: false, message: "登录完成检测超时" };
        }

        utils.log("DOUBAO_LOGIN: 自动登录流程完成");
        return { success: true, message: "登录成功" };

    } catch (error) {
        utils.log("DOUBAO_LOGIN: 自动登录失败: " + error, "error");
        return { success: false, message: "登录异常: " + error.toString() };
    }
};

module.exports = {
    DoubaoLoginManager: DoubaoLoginManager
};

utils.log("豆包登录模块加载完毕 (doubao_login.js)");
