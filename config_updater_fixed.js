/**
 * 配置更新工具 - Auto.js兼容版本
 * 将适配工具发现的新元素ID添加到设备兼容性配置中
 */

var utils = require('./utils.js');

/**
 * 配置更新器
 */
function ConfigUpdater() {
    this.newSelectors = {};
}

/**
 * 从适配结果文件中读取新的元素ID
 */
ConfigUpdater.prototype.loadAdapterResults = function(resultFilePath) {
    try {
        utils.log("CONFIG_UPDATER: 读取适配结果文件: " + resultFilePath);
        
        var content = files.read(resultFilePath);
        if (!content) {
            utils.log("CONFIG_UPDATER: 文件读取失败或为空");
            return false;
        }
        
        // 解析适配结果
        this.parseAdapterResults(content);
        return true;
        
    } catch (e) {
        utils.log("CONFIG_UPDATER: 读取适配结果失败: " + e.toString());
        return false;
    }
};

/**
 * 解析适配结果内容
 */
ConfigUpdater.prototype.parseAdapterResults = function(content) {
    var lines = content.split('\n');
    var currentElement = null;
    var currentElementData = {};
    
    for (var i = 0; i < lines.length; i++) {
        var line = lines[i];
        var trimmedLine = line.trim();
        
        // 检测元素名称
        if (trimmedLine.endsWith(':') && trimmedLine.indexOf('  ') === -1) {
            // 保存上一个元素
            if (currentElement && currentElementData.ID) {
                this.addNewSelector(currentElement, currentElementData);
            }
            
            // 开始新元素
            currentElement = trimmedLine.replace(':', '');
            currentElementData = {};
        }
        // 解析元素属性
        else if (trimmedLine.indexOf('ID: ') === 0) {
            currentElementData.ID = trimmedLine.replace('ID: ', '');
        }
        else if (trimmedLine.indexOf('Text: ') === 0) {
            currentElementData.Text = trimmedLine.replace('Text: ', '').replace(/"/g, '');
        }
        else if (trimmedLine.indexOf('Desc: ') === 0) {
            currentElementData.Desc = trimmedLine.replace('Desc: ', '').replace(/"/g, '');
        }
        else if (trimmedLine.indexOf('Class: ') === 0) {
            currentElementData.Class = trimmedLine.replace('Class: ', '');
        }
    }
    
    // 保存最后一个元素
    if (currentElement && currentElementData.ID) {
        this.addNewSelector(currentElement, currentElementData);
    }
    
    utils.log("CONFIG_UPDATER: 解析完成，发现 " + Object.keys(this.newSelectors).length + " 个新元素");
};

/**
 * 添加新的选择器
 */
ConfigUpdater.prototype.addNewSelector = function(elementName, elementData) {
    // 映射元素名称到配置键名
    var keyMapping = {
        // 搜索相关
        'searchBox': 'SEARCH_BOX',
        'searchButton': 'SEARCH_BUTTON',
        'filterButton': 'FILTER_BUTTON',
        
        // 笔记卡片
        'noteItems': 'NOTE_CARD_ITEM',
        'noteCardTitle': 'NOTE_CARD_TITLE',
        'noteCardAuthor': 'NOTE_CARD_AUTHOR',
        
        // 图文笔记
        'authorAvatar': 'IMAGE_NOTE_AUTHOR_AVATAR',
        'authorNickname': 'IMAGE_NOTE_AUTHOR_NICKNAME',
        'noteTitle': 'IMAGE_NOTE_TITLE',
        'noteContent': 'IMAGE_NOTE_CONTENT',
        'likeButton': 'IMAGE_NOTE_LIKE_BUTTON',
        'collectButton': 'IMAGE_NOTE_COLLECT_BUTTON',
        'commentButton': 'IMAGE_NOTE_COMMENT_BUTTON',
        'noteTypeIndicator': 'IMAGE_NOTE_TYPE_INDICATOR',
        'topContainer': 'IMAGE_NOTE_TOP_CONTAINER',
        'moreOperateButton': 'IMAGE_NOTE_MORE_OPERATE_BUTTON',
        
        // 视频笔记
        'wmContainer': 'VIDEO_NOTE_WM_CONTAINER',
        'c9yContainer': 'VIDEO_NOTE_C9Y_CONTAINER',
        'gg9Container': 'VIDEO_NOTE_GG9_CONTAINER',
        
        // 评论系统
        'commentAuthor': 'COMMENT_AUTHOR',
        'commentContent': 'COMMENT_CONTENT',
        'commentAvatar': 'COMMENT_AVATAR',
        'commentLikeButton': 'COMMENT_LIKE_BUTTON',
        'commentCountIndicator': 'COMMENT_COUNT_INDICATOR',
        'loadMoreComments': 'COMMENT_LOAD_MORE',
        'commentEndIndicator': 'COMMENT_END_INDICATOR',
        'singleCommentContainer': 'SINGLE_COMMENT_CONTAINER',
        'replyContainer': 'REPLY_CONTAINER',
        
        // 其他
        'copyLinkButton': 'COPY_LINK_BUTTON',
        'backButton': 'BACK_BUTTON'
    };
    
    var configKey = keyMapping[elementName];
    if (!configKey) {
        utils.log("CONFIG_UPDATER: 未知元素名称: " + elementName);
        return;
    }
    
    if (!this.newSelectors[configKey]) {
        this.newSelectors[configKey] = [];
    }
    
    // 创建新的选择器
    var newSelector = this.createSelector(elementData);
    if (newSelector) {
        this.newSelectors[configKey].push(newSelector);
        utils.log("CONFIG_UPDATER: 添加新选择器 " + configKey + ": " + newSelector.name);
    }
};

/**
 * 创建选择器对象
 */
ConfigUpdater.prototype.createSelector = function(elementData) {
    if (!elementData.ID) {
        return null;
    }
    
    // 生成选择器名称
    var deviceInfo = device.width + "x" + device.height;
    var idParts = elementData.ID.split('/');
    var shortId = idParts[idParts.length - 1];
    var selectorName = "新设备" + deviceInfo + "_" + shortId;
    
    // 生成选择器函数字符串
    var selectorFunctionStr;
    
    if (elementData.Class) {
        // 包含类名的精确选择器
        selectorFunctionStr = 'function() { return id("' + elementData.ID + '").className("' + elementData.Class + '").findOne(2000); }';
    } else {
        // 仅ID的选择器
        selectorFunctionStr = 'function() { return id("' + elementData.ID + '").findOne(2000); }';
    }
    
    return {
        name: selectorName,
        selectorStr: selectorFunctionStr,
        id: elementData.ID,
        className: elementData.Class || ''
    };
};

/**
 * 生成更新后的配置代码
 */
ConfigUpdater.prototype.generateUpdatedConfig = function() {
    var configCode = "// 更新后的设备兼容性配置\n";
    configCode += "// 自动生成时间: " + new Date().toISOString() + "\n\n";
    
    for (var configKey in this.newSelectors) {
        var selectors = this.newSelectors[configKey];
        configCode += "// 新增的 " + configKey + " 选择器:\n";
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            configCode += "{ name: \"" + selector.name + "\", selector: " + selector.selectorStr + " },\n";
        }
        configCode += "\n";
    }
    
    return configCode;
};

/**
 * 保存更新建议
 */
ConfigUpdater.prototype.saveUpdateSuggestions = function() {
    try {
        var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        var filename = "config_update_suggestions_" + timestamp + ".txt";
        
        var content = "=== 设备兼容性配置更新建议 ===\n";
        content += "生成时间: " + new Date().toISOString() + "\n";
        content += "设备信息: " + device.width + "x" + device.height + "\n\n";
        
        content += "📋 发现的新元素选择器:\n\n";
        
        for (var configKey in this.newSelectors) {
            var selectors = this.newSelectors[configKey];
            content += configKey + ":\n";
            for (var i = 0; i < selectors.length; i++) {
                content += "  - " + selectors[i].name + "\n";
                content += "    ID: " + selectors[i].id + "\n";
                if (selectors[i].className) {
                    content += "    Class: " + selectors[i].className + "\n";
                }
            }
            content += "\n";
        }
        
        content += "🛠️ 更新方法:\n\n";
        content += "1. 打开 device_compatibility.js 文件\n";
        content += "2. 找到对应的元素配置数组\n";
        content += "3. 将以下选择器添加到数组的开头:\n\n";
        
        content += this.generateUpdatedConfig();
        
        content += "\n💡 注意事项:\n";
        content += "1. 新选择器会自动添加到现有选择器列表的开头\n";
        content += "2. 智能查找器会优先尝试缓存的成功选择器\n";
        content += "3. 如果新选择器失效，会自动回退到其他选择器\n";
        content += "4. 建议定期清理无效的选择器以保持配置整洁\n";
        
        content += "\n📝 手动更新示例:\n";
        content += "在 device_compatibility.js 中找到对应的配置，例如:\n";
        content += "SEARCH_BOX: [\n";
        content += "    // 在这里添加新的选择器\n";
        content += "    { name: \"新设备选择器\", selector: () => id(\"新ID\").findOne(2000) },\n";
        content += "    // 保留原有的选择器作为备用\n";
        content += "    { name: \"原有选择器\", selector: () => ... },\n";
        content += "]\n";
        
        files.write(filename, content);
        utils.log("CONFIG_UPDATER: ✅ 更新建议已保存到: " + filename);
        
    } catch (e) {
        utils.log("CONFIG_UPDATER: 保存更新建议失败: " + e.toString());
    }
};

/**
 * 主函数 - 处理适配结果并生成更新建议
 */
function main() {
    var updater = new ConfigUpdater();
    
    // 获取最新的适配结果文件
    var filesList = files.listDir(".");
    var adapterFiles = [];
    
    for (var i = 0; i < filesList.length; i++) {
        var fileName = filesList[i];
        if (fileName.indexOf("adapter_result_") === 0 && fileName.indexOf(".txt") === fileName.length - 4) {
            adapterFiles.push(fileName);
        }
    }
    
    if (adapterFiles.length === 0) {
        dialogs.alert("提示", "未找到适配结果文件。请先运行适配工具生成结果文件。");
        return;
    }
    
    // 选择要处理的文件
    var choice = dialogs.select("选择适配结果文件", adapterFiles);
    if (choice === -1) {
        return;
    }
    
    var selectedFile = adapterFiles[choice];
    utils.log("CONFIG_UPDATER: 选择文件: " + selectedFile);
    
    // 加载并处理适配结果
    if (updater.loadAdapterResults(selectedFile)) {
        // 生成更新建议
        updater.saveUpdateSuggestions();
        
        dialogs.alert("完成", 
            "配置更新建议已生成！\n\n" +
            "请查看生成的建议文件，然后手动更新 device_compatibility.js 配置。\n\n" +
            "这样可以确保新设备和旧设备都能正常工作。"
        );
    } else {
        dialogs.alert("错误", "处理适配结果文件失败，请检查文件格式。");
    }
}

// 如果直接运行此文件，执行主函数
if (typeof module === 'undefined') {
    main();
}
