"ui";

// 1. 顶部声明所有模块变量为 `let`。实际的 require 将在下面的 try-catch 块中进行，以便捕获加载错误。
let configManager;
let utils;
let uiModule;
let xhsActions;
let detailedNoteNavigator;
let noteCommentingModule;
let llmService;
let shareLinkModule;
let safetyControl;
let accountManager;
// dataManager 在你的代码中是直接在顶部 require 且没有在后续 try-catch 中重新加载，
// 为保持原有行为和最小化改动，这里保留其原始声明方式，但如果它也需要错误捕获，也应该放进 try-catch。
const dataManager = require('./data_manager'); // 保持不变，因为它没有后续的 try-catch 块来捕获加载错误

// 确保所有在后续代码中被赋值或使用的全局变量都在顶部声明
let isScrapingComments = false;
let isCommentingNotes = false;
let processedNoteIdsThisSession = new Set();
let commentScrapingThread = null; // 确保 commentScrapingThread 在全局作用域声明
let collectedUsersCountThisSession = 0; // 确保 collectedUsersCountThisSession 在全局作用域声明
let processedNotesCountThisSession = 0; // 确保 processedNotesCountThisSession 在全局作用域声明
// xhsNoteNavigation 已移除 - 现在由 main.js 直接处理搜索结果页逻辑
let globalUIObject; // 确保 globalUIObject 在全局作用域声明

// 2. Load utils.js first (required by other modules)
try {
    console.log("MAIN_V3: Attempting to require utils.js...");
    utils = require('./utils.js');
    if (!(utils && typeof utils.log === 'function')) {
        throw new Error("utils.js loaded but essential function log is missing.");
    }
    console.log("MAIN_V3: utils.js loaded successfully.");
} catch (e_utils_load) {
    let utilsLoadErrMsg = "MAIN_V3: FATAL ERROR loading utils.js: " + e_utils_load.toString();
    console.error(utilsLoadErrMsg);
    console.error(e_utils_load.stack);
    toast(utilsLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + utilsLoadErrMsg + "\nStack: " + e_utils_load.stack + "\n"); } catch (fe) { }
    throw e_utils_load;
}

// 3. Load config.js
try {
    utils.log("MAIN_V3: Attempting to require config.js...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    configManager = require('./config');

    // 修正: if 语句的括号问题
    if (!(configManager &&
        typeof configManager.saveConfig === 'function' &&
        typeof configManager.loadConfig === 'function' &&
        typeof configManager.getCommentScrapingConfig === 'function'
    )) {
        throw new Error("config.js loaded but essential functions (saveConfig, loadConfig, getCommentScrapingConfig) are missing.");
    }
    utils.log("MAIN_V3: config.js loaded successfully.");
} catch (e_config_load) {
    let configLoadErrMsg = "MAIN_V3: FATAL ERROR loading config.js: " + e_config_load.toString();
    utils.log(configLoadErrMsg); console.error(configLoadErrMsg); console.error(e_config_load.stack); toast(configLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + configLoadErrMsg + "\nStack: " + e_config_load.stack + "\n"); } catch (fe) { }
    throw e_config_load;
}

// 4. Load ui.js
try {
    utils.log("MAIN_V3: Attempting to require ui.js...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    uiModule = require('./ui.js');
    if (!(uiModule &&
        typeof uiModule.getUILayoutXML === 'function' &&
        typeof uiModule.attachUIEventHandlers === 'function' &&
        typeof uiModule.updateScrapingStatus === 'function' &&
        typeof uiModule.updateProcessedNotesCount === 'function' &&
        typeof uiModule.updateCollectedUsersCount === 'function'
    )) {
        throw new Error("ui.js loaded but essential functions (getUILayoutXML, attachUIEventHandlers, UI updaters) are missing.");
    }
    utils.log("MAIN_V3: ui.js loaded successfully.");
} catch (e_ui_module_load) {
    let uiModuleLoadErrMsg = "MAIN_V3: FATAL ERROR loading ui.js: " + e_ui_module_load.toString();
    utils.log(uiModuleLoadErrMsg); console.error(uiModuleLoadErrMsg); console.error(e_ui_module_load.stack); toast(uiModuleLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + uiModuleLoadErrMsg + "\nStack: " + e_ui_module_load.stack + "\n"); } catch (fe) { }
    throw e_ui_module_load;
}

// 4.5. Load xhs_actions.js
try {
    utils.log("MAIN_V3: Attempting to require xhs_actions.js...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    xhsActions = require('./xhs_actions.js');
    if (!(xhsActions &&
        typeof xhsActions.searchNotes === 'function' &&
        typeof xhsActions.navigateToNextNoteFromSearchResults === 'function' &&
        typeof xhsActions.backToPreviousPage === 'function' &&
        typeof xhsActions.extractVisibleNotes_AdaptedOfficial === 'function'
    )) {
        throw new Error("xhs_actions.js loaded but essential functions (searchNotes, navigation, backToPreviousPage, extractVisibleNotes) are missing.");
    }
    utils.log("MAIN_V3: xhs_actions.js loaded successfully.");
} catch (e_xhs_actions_load) {
    let xhsActionsLoadErrMsg = "MAIN_V3: FATAL ERROR loading xhs_actions.js: " + e_xhs_actions_load.toString();
    utils.log(xhsActionsLoadErrMsg); console.error(xhsActionsLoadErrMsg); console.error(e_xhs_actions_load.stack); toast(xhsActionsLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + xhsActionsLoadErrMsg + "\nStack: " + e_xhs_actions_load.stack + "\n"); } catch (fe) { }
    throw e_xhs_actions_load;
}

// 4.6. Load xhs_note_commenting.js
try {
    utils.log("MAIN_V3: Attempting to require xhs_note_commenting.js...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    noteCommentingModule = require('./xhs_note_commenting.js');
    if (!(noteCommentingModule && typeof noteCommentingModule.publishCommentInCurrentNote === 'function')) { // Check for a known function
        throw new Error("xhs_note_commenting.js loaded but essential functions (e.g., publishCommentInCurrentNote) are missing.");
    }
    utils.log("MAIN_V3: xhs_note_commenting.js loaded successfully.");
} catch (e_note_commenting_load) {
    let noteCommentingLoadErrMsg = "MAIN_V3: FATAL ERROR loading xhs_note_commenting.js: " + e_note_commenting_load.toString();
    utils.log(noteCommentingLoadErrMsg); console.error(noteCommentingLoadErrMsg); console.error(e_note_commenting_load.stack); toast(noteCommentingLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + noteCommentingLoadErrMsg + "\nStack: " + e_note_commenting_load.stack + "\n"); } catch (fe) { }
    throw e_note_commenting_load;
}

// 4.7. Load llm_service.js
try {
    utils.log("MAIN_V3: Attempting to require llm_service.js...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    llmService = require('./llm_service.js');
    if (!(llmService && typeof llmService.generateCommentWithLLM === 'function')) {
        throw new Error("llm_service.js loaded but essential function generateCommentWithLLM is missing.");
    }
    utils.log("MAIN_V3: llm_service.js loaded successfully.");
} catch (e_llm_load) {
    let llmLoadErrMsg = "MAIN_V3: FATAL ERROR loading llm_service.js: " + e_llm_load.toString();
    utils.log(llmLoadErrMsg); console.error(llmLoadErrMsg); console.error(e_llm_load.stack); toast(llmLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + llmLoadErrMsg + "\nStack: " + e_llm_load.stack + "\n"); } catch (fe) { }
    throw e_llm_load;
}

// 4.8. Load llm_retry_service.js
let llmRetryService;
try {
    utils.log("MAIN_V3: Attempting to require llm_retry_service.js...");
    llmRetryService = require('./llm_retry_service.js');
    if (!(llmRetryService && typeof llmRetryService.generateCommentWithRetry === 'function')) {
        throw new Error("llm_retry_service.js loaded but essential function generateCommentWithRetry is missing.");
    }
    utils.log("MAIN_V3: llm_retry_service.js loaded successfully.");
} catch (e_llm_retry_load) {
    let llmRetryLoadErrMsg = "MAIN_V3: FATAL ERROR loading llm_retry_service.js: " + e_llm_retry_load.toString();
    utils.log(llmRetryLoadErrMsg); console.error(llmRetryLoadErrMsg); console.error(e_llm_retry_load.stack); toast(llmRetryLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + llmRetryLoadErrMsg + "\nStack: " + e_llm_retry_load.stack + "\n"); } catch (fe) { }
    throw e_llm_retry_load;
}

// 4.8. Load note_navigation.js (for detailed note info extraction)
try {
    utils.log("MAIN_V3: Attempting to require note_navigation.js (for detailed info)...");
    // 修正: 将 require 移到 try 块内部，并赋值给顶部声明的 let 变量
    detailedNoteNavigator = require('./note_navigation.js');
    if (!(detailedNoteNavigator && typeof detailedNoteNavigator.getCurrentNoteInfo === 'function')) {
        throw new Error("note_navigation.js loaded but essential function getCurrentNoteInfo is missing.");
    }
    utils.log("MAIN_V3: note_navigation.js (detailed info) loaded successfully.");
} catch (e_detail_nav_load) {
    let detailNavLoadErrMsg = "MAIN_V3: FATAL ERROR loading note_navigation.js (detailed info): " + e_detail_nav_load.toString();
    utils.log(detailNavLoadErrMsg); console.error(detailNavLoadErrMsg); console.error(e_detail_nav_load.stack); toast(detailNavLoadErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + detailNavLoadErrMsg + "\nStack: " + e_detail_nav_load.stack + "\n"); } catch (fe) { }
    throw e_detail_nav_load;
}

// xhs_note_navigation.js 模块已移除
// 现在由 main.js 直接处理搜索结果页逻辑，各个笔记类型由专门模块处理

// 4.10. Load xhs_share_link.js (for share link copying functionality)
try {
    utils.log("MAIN_V3: Attempting to require xhs_share_link.js...");
    shareLinkModule = require('./xhs_share_link.js');
    if (!(shareLinkModule && typeof shareLinkModule.copyCurrentNoteShareLink === 'function')) {
        throw new Error("xhs_share_link.js loaded but essential function copyCurrentNoteShareLink is missing.");
    }
    utils.log("MAIN_V3: xhs_share_link.js loaded successfully.");
} catch (e_share_link_load) {
    let shareLinkLoadErrMsg = "MAIN_V3: FATAL ERROR loading xhs_share_link.js: " + e_share_link_load.toString();
    utils.log(shareLinkLoadErrMsg); console.error(shareLinkLoadErrMsg); console.error(e_share_link_load.stack); toast(shareLinkLoadErrMsg);
    // Not throwing fatal, as this is optional functionality
}

// 4.11. Load xhs_safety_control.js (for safety controls)
try {
    utils.log("MAIN_V3: Attempting to require xhs_safety_control.js...");
    safetyControl = require('./xhs_safety_control.js');
    if (!(safetyControl && typeof safetyControl.performSafetyCheck === 'function')) {
        throw new Error("xhs_safety_control.js loaded but essential function performSafetyCheck is missing.");
    }
    // 初始化安全控制
    safetyControl.initSafetyControl();
    utils.log("MAIN_V3: xhs_safety_control.js loaded and initialized successfully.");
} catch (e_safety_load) {
    let safetyLoadErrMsg = "MAIN_V3: FATAL ERROR loading xhs_safety_control.js: " + e_safety_load.toString();
    utils.log(safetyLoadErrMsg); console.error(safetyLoadErrMsg); console.error(e_safety_load.stack); toast(safetyLoadErrMsg);
    // Not throwing fatal, as this is optional functionality
}

// 4.12. Load xhs_account_manager.js (for multi-account management)
try {
    utils.log("MAIN_V3: Attempting to require xhs_account_manager.js...");
    accountManager = require('./xhs_account_manager.js');
    if (!(accountManager && typeof accountManager.switchToNextAccount === 'function')) {
        throw new Error("xhs_account_manager.js loaded but essential function switchToNextAccount is missing.");
    }
    // 初始化账号管理器
    accountManager.initAccountManager();
    utils.log("MAIN_V3: xhs_account_manager.js loaded and initialized successfully.");
} catch (e_account_load) {
    let accountLoadErrMsg = "MAIN_V3: FATAL ERROR loading xhs_account_manager.js: " + e_account_load.toString();
    utils.log(accountLoadErrMsg); console.error(accountLoadErrMsg); console.error(e_account_load.stack); toast(accountLoadErrMsg);
    // Not throwing fatal, as this is optional functionality
}


// --- UI Updaters (main.js specific wrappers) ---
function mainUiLogUpdater(message) {
    if (globalUIObject && globalUIObject.logText) {
        globalUIObject.run(() => {
            let currentLog = globalUIObject.logText.getText().toString();
            const maxLogLines = 200;
            let logLines = currentLog.split('\n');
            if (logLines.length > maxLogLines) {
                logLines = logLines.slice(logLines.length - maxLogLines);
            }
            currentLog = logLines.join('\n');
            globalUIObject.logText.setText(currentLog + "\n" + new Date().toLocaleTimeString() + ": " + message);
        });
    } else { console.warn("MAIN_V3_LOG_UPDATER: globalUIObject.logText not available."); }
}

// 此函数用于其他任务类型，目前评论采集功能不直接使用
// 保留此函数以供将来扩展功能使用
/*
function mainUiStatusListUpdater(usersArray) {
    if (globalUIObject && globalUIObject.statusList) {
        globalUIObject.statusList.setDataSource(usersArray);
        utils.log("MAIN_V3: Status list updated with " + usersArray.length + " users.");
    } else { console.warn("MAIN_V3_STATUS_UPDATER: globalUIObject.statusList not available."); }
}
*/

function updateScrapingStatusDisplay(statusText) {
    if (uiModule && typeof uiModule.updateScrapingStatus === 'function' && globalUIObject) {
        uiModule.updateScrapingStatus(globalUIObject, statusText);
    } else { utils.log("MAIN_V3_UI_UPDATE_WARN: uiModule.updateScrapingStatus unavailable."); }
}
function updateProcessedNotesCountDisplay(count) {
    if (uiModule && typeof uiModule.updateProcessedNotesCount === 'function' && globalUIObject) {
        uiModule.updateProcessedNotesCount(globalUIObject, count);
    } else { utils.log("MAIN_V3_UI_UPDATE_WARN: uiModule.updateProcessedNotesCount unavailable."); }
}
function updateCollectedUsersCountDisplay(count) {
    if (uiModule && typeof uiModule.updateCollectedUsersCount === 'function' && globalUIObject) {
        uiModule.updateCollectedUsersCount(globalUIObject, count);
    } else { utils.log("MAIN_V3_UI_UPDATE_WARN: uiModule.updateCollectedUsersCount unavailable."); }
}

function updateUserStatisticsDisplay() {
    if (uiModule && typeof uiModule.updateUserStatisticsDisplay === 'function' && globalUIObject) {
        uiModule.updateUserStatisticsDisplay(globalUIObject, dataManager);
    } else { utils.log("MAIN_V3_UI_UPDATE_WARN: uiModule.updateUserStatisticsDisplay unavailable."); }
}


// --- Comment Scraping Logic ---
// Removed debug log line that was here.
// Removed top-level var commentScrapingLoop = async function () { ... }; for debugging


// --- Main Task Manager for UI Callbacks ---
const mainTaskManager = {
    // 检查当前是否在搜索结果页面
    isSearchResultsPage: function () {
        if (xhsActions && typeof xhsActions.isSearchResultsPage === 'function') {
            return xhsActions.isSearchResultsPage();
        }
        return false;
    },

    handleSearchRequest: function (searchParams) {
        utils.log("MAIN_V3: Received search request: " + JSON.stringify(searchParams));
        mainUiLogUpdater("开始执行笔记搜索...");
        toast("正在搜索笔记...");

        if (!xhsActions || typeof xhsActions.searchNotes !== 'function') {
            utils.log("ERROR: MAIN_V3: xhsActions.searchNotes is not available!");
            toast("错误：搜索功能模块未正确加载。");
            mainUiLogUpdater("错误：搜索功能模块未正确加载。");
            return { success: false, message: "搜索功能模块未正确加载" };
        }

        try {
            const searchResult = xhsActions.searchNotes(
                searchParams.keyword, searchParams.sortBy, searchParams.publishTime, searchParams.locationDistance
            );
            if (searchResult.success) {
                utils.log("MAIN_V3: 搜索成功。 " + searchResult.message);
                toast("搜索成功！");
                mainUiLogUpdater("搜索成功: " + searchResult.message);
            } else {
                utils.log("ERROR: MAIN_V3: 搜索失败: " + searchResult.message);
                toast("搜索失败: " + searchResult.message);
                mainUiLogUpdater("搜索失败: " + searchResult.message);
            }
            return searchResult;
        } catch (e_search) {
            let searchExecErrorMsg = "MAIN_V3: 执行搜索时发生严重错误: " + e_search.toString();
            utils.log("ERROR: " + searchExecErrorMsg);
            console.error(searchExecErrorMsg);
            console.error(e_search.stack);
            toast("搜索执行出错！");
            mainUiLogUpdater("搜索执行出错: " + e_search.toString());
            return { success: false, message: "执行搜索时发生错误: " + e_search.toString() };
        }
    },

    startCommentUserScraping: function () {
        utils.log("MAIN_V3: Start comment user scraping requested.");
        if (isScrapingComments) {
            toast("评论采集任务已在运行中。");
            utils.log("MAIN_V3: Comment scraping already running.");
            return;
        }
        const scrapingConfig = configManager.getCommentScrapingConfig();
        if (!scrapingConfig || !scrapingConfig.commentKeywords || scrapingConfig.commentKeywords.trim() === "") {
            toast("请先在配置中输入评论区筛选关键词！");
            utils.log("MAIN_V3: Comment keywords not set. Aborting start.");
            updateScrapingStatusDisplay("错误: 未设置关键词");
            return;
        }



        isScrapingComments = true;
        processedNoteIdsThisSession.clear();
        collectedUsersCountThisSession = 0;
        processedNotesCountThisSession = 0;

        updateScrapingStatusDisplay("采集中...");
        updateProcessedNotesCountDisplay(0);
        updateCollectedUsersCountDisplay(0); // Display total collected users if needed from dataManager.getScrapedCommentUsers().length
        utils.log("MAIN_V3: Initialized states for comment scraping.");

        commentScrapingThread = threads.start(function () {
            utils.log("MAIN_V3: 评论采集线程已启动。");

            // 使用简化版的评论采集函数，避免在线程中使用async/await
            function commentScrapingLoop() {
                utils.log("MAIN_V3: 开始评论采集循环。");

                try {
                    const scrapingConfig = configManager.getCommentScrapingConfig();
                    const commentKeywords = scrapingConfig.commentKeywords || "";

                    // 获取目标区域配置
                    const fullConfig = configManager.loadConfig();
                    const targetRegionIndex = fullConfig.targetRegion || 0;

                    utils.log("MAIN_V3: 采集配置 - 关键词: " + commentKeywords + ", 目标区域: " + utils.getRegionNameByIndex(targetRegionIndex));
                    updateScrapingStatusDisplay("正在采集...");

                    // 确保在搜索结果页
                    if (!xhsActions.isSearchResultsPage()) {
                        utils.log("MAIN_V3: 当前不在搜索结果页面，无法开始评论采集。");
                        updateScrapingStatusDisplay("错误: 不在搜索结果页");
                        isScrapingComments = false;
                        return;
                    }

                    // 递归处理所有屏幕的笔记
                    function processAllScreensNotes() {
                        if (!isScrapingComments) {
                            utils.log("MAIN_V3: 采集已停止，退出处理循环");
                            updateScrapingStatusDisplay("已停止");
                            commentScrapingThread = null;
                            return;
                        }

                        utils.log("MAIN_V3: 开始处理当前屏幕内的所有笔记...");
                        updateScrapingStatusDisplay("正在处理当前屏幕...");

                        // 评论采集功能：获取当前屏幕的笔记并进行采集
                        try {
                            const notesOnScreen = xhsActions.extractVisibleNotes_AdaptedOfficial();
                            if (!notesOnScreen || notesOnScreen.length === 0) {
                                utils.log("MAIN_V3: 当前屏幕未找到笔记");
                                if (xhsActions.isSearchResultsPageAtBottom()) {
                                    utils.log("MAIN_V3: 已到达底部，采集完成");
                                    updateScrapingStatusDisplay("采集完成");
                                    toast("采集完成！总共处理 " + processedNotesCountThisSession + " 个笔记，采集 " + collectedUsersCountThisSession + " 条评论");
                                    isScrapingComments = false;
                                    commentScrapingThread = null;
                                    return;
                                }
                            } else {
                                utils.log("MAIN_V3: 找到 " + notesOnScreen.length + " 个笔记，开始采集");

                                var processedCount = 0;
                                var totalComments = 0;

                                for (let i = 0; i < notesOnScreen.length; i++) {
                                    if (!isScrapingComments) break;

                                    const noteInfo = notesOnScreen[i];

                                    // 检查是否已处理过此笔记
                                    utils.log("MAIN_V3: 检查笔记去重 - signature: " + noteInfo.signature);
                                    utils.log("MAIN_V3: 当前已处理笔记数量: " + processedNoteIdsThisSession.size);
                                    if (processedNoteIdsThisSession.has(noteInfo.signature)) {
                                        utils.log("MAIN_V3: 笔记已处理过，跳过: " + noteInfo.signature.substring(0, 50) + "...");
                                        continue;
                                    }
                                    utils.log("MAIN_V3: 笔记未处理过，继续处理");

                                    utils.log("MAIN_V3: 采集笔记: " + noteInfo.title);

                                    // 点击进入笔记
                                    if (noteInfo.clickableElement && noteInfo.clickableElement.click()) {
                                        sleep(3000 + Math.random() * 2000);

                                        if (xhsActions.isNoteDetailPage()) {
                                            utils.log("MAIN_V3: 成功进入笔记详情页，开始采集评论");

                                            // 检测笔记类型并调用对应的采集模块
                                            const noteTypes = require('./xhs_note_types.js');
                                            const currentNoteType = noteTypes.detectNoteTypeInDetailPage();

                                            var commentsCollected = 0;
                                            if (currentNoteType === noteTypes.NOTE_TYPES.IMAGE_TEXT) {
                                                // 图文笔记评论采集
                                                const simpleComments = require('./xhs_simple_comments.js');
                                                commentsCollected = simpleComments.collectAndSaveComments(noteInfo.title, commentKeywords, targetRegionIndex, false);
                                            } else if (currentNoteType === noteTypes.NOTE_TYPES.VIDEO) {
                                                // 视频笔记评论采集
                                                const videoComments = require('./xhs_video_comments.js');
                                                commentsCollected = videoComments.collectVideoNoteComments(noteInfo.title, commentKeywords, targetRegionIndex, false);
                                            }

                                            if (commentsCollected > 0) {
                                                processedCount++;
                                                totalComments += commentsCollected;
                                                utils.log("MAIN_V3: 采集成功，本笔记采集 " + commentsCollected + " 条评论");
                                            }

                                            // 标记笔记为已处理（无论是否采集到评论）
                                            processedNoteIdsThisSession.add(noteInfo.signature);
                                            utils.log("MAIN_V3: 笔记已标记为已处理: " + noteInfo.signature.substring(0, 50) + "...");

                                            // 返回搜索结果页
                                            var backSuccess = xhsActions.backToPreviousPage(currentNoteType);
                                            if (!backSuccess) {
                                                utils.log("MAIN_V3: 返回失败，使用备用方法");
                                                back();
                                                sleep(1000);
                                            }

                                            sleep(1500 + Math.random() * 1000);
                                        } else {
                                            utils.log("MAIN_V3: 未能进入笔记详情页");
                                            // 标记为已处理，避免重复尝试
                                            processedNoteIdsThisSession.add(noteInfo.signature);
                                        }
                                    } else {
                                        utils.log("MAIN_V3: 点击笔记失败");
                                        // 标记为已处理，避免重复尝试
                                        processedNoteIdsThisSession.add(noteInfo.signature);
                                    }
                                }

                                utils.log("MAIN_V3: 当前屏幕处理完成。处理了 " + processedCount + " 个笔记，采集了 " + totalComments + " 条评论");

                                // 更新UI显示
                                processedNotesCountThisSession += processedCount;
                                collectedUsersCountThisSession += totalComments;
                                updateProcessedNotesCountDisplay(processedNotesCountThisSession);
                                updateCollectedUsersCountDisplay(collectedUsersCountThisSession);
                                // 更新用户统计信息
                                updateUserStatisticsDisplay();
                            }

                            // 检查是否到达底部
                            if (xhsActions.isSearchResultsPageAtBottom()) {
                                utils.log("MAIN_V3: 已到达搜索结果页面底部，采集完成");
                                updateScrapingStatusDisplay("采集完成");
                                toast("采集完成！总共处理 " + processedNotesCountThisSession + " 个笔记，采集 " + collectedUsersCountThisSession + " 条评论");
                                isScrapingComments = false;
                                commentScrapingThread = null;
                                return;
                            }

                            // 滚动到下一页
                            utils.log("MAIN_V3: 当前屏幕处理完成，尝试下滚加载更多笔记...");
                            updateScrapingStatusDisplay("下滚加载更多...");

                            var scrollSuccess = xhsActions.scrollSearchResultsPage();
                            if (scrollSuccess) {
                                utils.log("MAIN_V3: 下滚成功，等待新内容加载后继续处理...");
                                sleep(2000); // 等待新内容加载
                                processAllScreensNotes(); // 递归处理下一屏
                            } else {
                                utils.log("MAIN_V3: 下滚失败，采集结束");
                                updateScrapingStatusDisplay("下滚失败，采集结束");
                                isScrapingComments = false;
                                commentScrapingThread = null;
                            }

                        } catch (processError) {
                            utils.log("MAIN_V3: 当前屏幕处理发生异常: " + processError.toString());
                            updateScrapingStatusDisplay("异常: " + processError.toString().substring(0, 20));
                            isScrapingComments = false;
                            commentScrapingThread = null;
                        }
                    }

                    // 开始处理
                    processAllScreensNotes();

                } catch (e) {
                    utils.log("MAIN_V3: 评论采集初始化发生错误: " + e.toString());
                    updateScrapingStatusDisplay("错误: " + e.toString().substring(0, 20));
                    isScrapingComments = false;
                }
            }
            // 开始评论采集循环
            commentScrapingLoop();
        });
        toast("评论采集任务已启动。");
    },

    stopCommentUserScraping: function () {
        utils.log("MAIN_V3: Stop comment user scraping requested.");
        if (!isScrapingComments && !commentScrapingThread) {
            toast("采集任务未在运行。");
            return;
        }
        isScrapingComments = false; // Signal the loop to stop
        updateScrapingStatusDisplay("停止中...");
        // The loop will check 'isScrapingComments' and exit.
        // If thread needs forceful interruption (not always safe):
        // if (commentScrapingThread && commentScrapingThread.isAlive()) {
        //     commentScrapingThread.interrupt();
        // }
        toast("评论采集任务将在当前操作完成后停止。");
        utils.log("MAIN_V3: Comment scraping task flagged to stop.");
    },

    startNoteCommenting: function () {
        utils.log("MAIN_V3_COMMENTING: startNoteCommenting task initiated by UI.");
        mainUiLogUpdater("笔记截流(评论笔记)任务启动中...");

        if (isCommentingNotes) {
            toast("笔记评论任务已在运行中。");
            utils.log("MAIN_V3_COMMENTING: Task already running.");
            return;
        }

        const currentTaskConfig = configManager.loadConfig();
        if (!currentTaskConfig.task_comment_notes) {
            toast("笔记评论任务未在配置中启用。");
            utils.log("MAIN_V3_COMMENTING: Task not enabled in config.");
            mainUiLogUpdater("笔记评论任务未启用。");
            return;
        }



        if (!noteCommentingModule || typeof noteCommentingModule.publishCommentInCurrentNote !== 'function') {
            utils.logError("MAIN_V3_COMMENTING: noteCommentingModule.publishCommentInCurrentNote is not available!");
            toast("错误：评论功能模块未正确加载。");
            mainUiLogUpdater("错误：评论功能模块未正确加载。");
            return;
        }
        if (!detailedNoteNavigator || typeof detailedNoteNavigator.getCurrentNoteInfo !== 'function') {
            utils.logError("MAIN_V3_COMMENTING: detailedNoteNavigator.getCurrentNoteInfo is not available!");
            toast("错误：笔记内容提取模块未正确加载。");
            mainUiLogUpdater("错误：笔记内容提取模块未正确加载。");
            return;
        }
        if (currentTaskConfig.enableLlmComments && (!llmService || typeof llmService.generateCommentWithLLM !== 'function')) {
            utils.logError("MAIN_V3_COMMENTING: llmService.generateCommentWithLLM is not available but LLM comments are enabled!");
            toast("错误：LLM服务模块未正确加载。");
            mainUiLogUpdater("错误：LLM服务模块未正确加载。");
            return;
        }
        // 检查 xhsActions 模块的必要函数
        if (!xhsActions || typeof xhsActions.extractVisibleNotes_AdaptedOfficial !== 'function' || typeof xhsActions.backToPreviousPage !== 'function' || typeof xhsActions.scrollSearchResultsPage !== 'function' || typeof xhsActions.isSearchResultsPageAtBottom !== 'function') {
            utils.logError("MAIN_V3_COMMENTING: Essential functions from xhsActions (extractVisibleNotes, navigation) are missing!");
            toast("错误：核心导航操作模块不完整。");
            mainUiLogUpdater("错误：核心导航操作模块不完整。");
            return;
        }


        isCommentingNotes = true;
        processedNoteIdsThisSession.clear(); // Use the same set for now, or a new one for this task type
        let notesCommentedThisSession = 0;
        updateScrapingStatusDisplay("笔记评论任务启动..."); // Or a new UI field

        // 修正: 这是唯一的、正确的线程启动和其内部的评论逻辑。
        // 已删除之前所有重复的线程逻辑。
        threads.start(function () {
            utils.log("MAIN_V3_COMMENTING: Note commenting thread started.");
            mainUiLogUpdater("笔记评论线程已启动。");

            try {
                // 使用同步处理逻辑，完全避免异步
                while (isCommentingNotes) {
                    if (!xhsActions.isSearchResultsPage()) {
                        utils.log("MAIN_V3_COMMENTING: Not on a search results page. Stopping task.");
                        mainUiLogUpdater("错误: 未在搜索结果页，任务停止。");
                        break;
                    }

                    utils.log("MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...");
                    updateScrapingStatusDisplay("正在处理当前屏幕...");

                    try {
                        // 获取当前屏幕的笔记
                        const notesOnScreen = xhsActions.extractVisibleNotes_AdaptedOfficial();
                        if (!notesOnScreen || notesOnScreen.length === 0) {
                            utils.log("MAIN_V3_COMMENTING: 当前屏幕未找到笔记");
                            if (xhsActions.isSearchResultsPageAtBottom()) {
                                utils.log("MAIN_V3_COMMENTING: 已到达底部，任务完成");
                                mainUiLogUpdater("已到达底部，任务完成");
                                break;
                            }
                        } else {
                            utils.log("MAIN_V3_COMMENTING: 找到 " + notesOnScreen.length + " 个笔记，开始处理");

                            var processedCount = 0;
                            var foundUnprocessedNote = false;

                            for (let i = 0; i < notesOnScreen.length; i++) {
                                if (!isCommentingNotes) break;

                                // 使用var而不是const，避免Auto.js的引用问题
                                var noteInfo = notesOnScreen[i];
                                var noteTitle = noteInfo.title;
                                var noteAuthor = noteInfo.author;
                                var noteSignature = noteInfo.signature;
                                var noteCommentCount = noteInfo.commentCount || 0;

                                // 添加详细调试信息
                                utils.log("MAIN_V3_COMMENTING: 检查笔记 " + (i + 1) + "/" + notesOnScreen.length);
                                utils.log("MAIN_V3_COMMENTING: 笔记标题: \"" + noteTitle + "\"");
                                utils.log("MAIN_V3_COMMENTING: 笔记作者: \"" + noteAuthor + "\"");
                                utils.log("MAIN_V3_COMMENTING: 笔记评论数: " + noteCommentCount);
                                utils.log("MAIN_V3_COMMENTING: 笔记signature: \"" + noteSignature + "\"");

                                // 去重检查：如果笔记已经处理过，跳过
                                if (processedNoteIdsThisSession.has(noteSignature)) {
                                    utils.log("MAIN_V3_COMMENTING: 笔记已处理过，跳过: " + noteSignature.substring(0, 50) + "...");
                                    continue;
                                }

                                // 安全检查：检查笔记是否满足安全要求
                                if (safetyControl) {
                                    const safetyConfig = configManager.getNoteCommentingConfig();
                                    const safetyCheckResult = safetyControl.performSafetyCheck(noteInfo, safetyConfig);

                                    if (!safetyCheckResult.passed) {
                                        utils.log("MAIN_V3_COMMENTING: 安全检查未通过: " + safetyCheckResult.reason);

                                        // 检查是否是因为达到评论限制，如果是且启用了多账号，尝试切换账号
                                        if (safetyCheckResult.reason.includes("已达每日评论次数限制") &&
                                            accountManager && safetyConfig.enableMultiAccount && safetyConfig.autoSwitchOnLimit) {

                                            utils.log("MAIN_V3_COMMENTING: 尝试自动切换账号...");
                                            mainUiLogUpdater("达到评论限制，正在切换账号...");

                                            const switchResult = accountManager.switchToNextAccount();
                                            if (switchResult) {
                                                utils.log("MAIN_V3_COMMENTING: 账号切换成功，重新开始任务");
                                                mainUiLogUpdater("账号切换成功，继续任务");

                                                // 重置安全控制的评论计数
                                                if (accountManager.resetCurrentAccountCommentCount) {
                                                    accountManager.resetCurrentAccountCommentCount();
                                                }

                                                // 重新开始搜索和评论流程
                                                utils.log("MAIN_V3_COMMENTING: 账号切换后重新开始任务");
                                                break; // 跳出当前笔记循环，重新开始
                                            } else {
                                                utils.log("MAIN_V3_COMMENTING: 账号切换失败，停止任务");
                                                mainUiLogUpdater("账号切换失败，任务停止");
                                                isCommentingNotes = false;
                                                break;
                                            }
                                        } else {
                                            mainUiLogUpdater("跳过笔记: " + safetyCheckResult.reason);
                                            continue;
                                        }
                                    } else {
                                        utils.log("MAIN_V3_COMMENTING: 安全检查通过");
                                    }
                                }

                                foundUnprocessedNote = true;
                                utils.log("MAIN_V3_COMMENTING: 处理笔记: " + noteTitle);
                                mainUiLogUpdater("处理笔记: " + noteTitle.substring(0, 20) + "...");

                                // 点击进入笔记
                                if (noteInfo.clickableElement && noteInfo.clickableElement.click()) {
                                    sleep(3000 + Math.random() * 2000);

                                    if (xhsActions.isNoteDetailPage()) {
                                        utils.log("MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...");

                                        // 检测笔记类型并调用对应的处理模块
                                        const noteTypes = require('./xhs_note_types.js');
                                        const currentNoteType = noteTypes.detectNoteTypeInDetailPage();

                                        var noteProcessSuccess = false;
                                        utils.log("MAIN_V3_COMMENTING: 检测到笔记类型: " + currentNoteType);

                                        if (currentNoteType === noteTypes.NOTE_TYPES.IMAGE_TEXT) {
                                            // 图文笔记处理
                                            utils.log("MAIN_V3_COMMENTING: 调用图文笔记处理模块");
                                            const simpleComments = require('./xhs_simple_comments.js');
                                            noteProcessSuccess = simpleComments.processImageTextNote(noteTitle, noteAuthor);
                                        } else if (currentNoteType === noteTypes.NOTE_TYPES.VIDEO) {
                                            // 视频笔记处理
                                            utils.log("MAIN_V3_COMMENTING: 调用视频笔记处理模块");
                                            const videoComments = require('./xhs_video_comments.js');
                                            noteProcessSuccess = videoComments.processVideoNote(noteTitle, noteAuthor);
                                        } else {
                                            utils.log("MAIN_V3_COMMENTING: 未知笔记类型 (" + currentNoteType + ")，跳过处理");
                                        }

                                        // 标记为已处理
                                        processedNoteIdsThisSession.add(noteSignature);
                                        utils.log("MAIN_V3_COMMENTING: 笔记已标记为已处理: " + noteSignature.substring(0, 50) + "...");

                                        if (noteProcessSuccess) {
                                            processedCount++;
                                            notesCommentedThisSession++;

                                            // 记录安全控制操作
                                            if (safetyControl) {
                                                safetyControl.executePostCommentSafety();
                                            }

                                            utils.log("MAIN_V3_COMMENTING: 笔记处理成功，总计: " + notesCommentedThisSession);
                                            mainUiLogUpdater("处理成功! 总计: " + notesCommentedThisSession);
                                        } else {
                                            utils.log("MAIN_V3_COMMENTING: 笔记处理失败或跳过");
                                        }

                                        // 注意：返回搜索结果页由具体的笔记处理模块负责
                                        sleep(1000 + Math.random() * 500);
                                    } else {
                                        utils.log("MAIN_V3_COMMENTING: 未能进入笔记详情页");
                                    }
                                } else {
                                    utils.log("MAIN_V3_COMMENTING: 点击笔记失败");
                                }
                            }

                            utils.log("MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 " + processedCount + " 个笔记");
                            mainUiLogUpdater("当前屏幕完成，处理数: " + notesCommentedThisSession);

                            // 如果当前屏幕没有找到未处理的笔记，需要滚动
                            if (!foundUnprocessedNote) {
                                utils.log("MAIN_V3_COMMENTING: 当前屏幕所有笔记都已处理过，准备滚动到下一页");
                            }
                        }
                    } catch (processError) {
                        utils.log("MAIN_V3_COMMENTING: 处理当前屏幕时发生异常: " + processError);
                        mainUiLogUpdater("处理异常: " + processError.toString().substring(0, 30));
                    }

                    if (!isCommentingNotes) break;

                    // 滚动到下一页
                    mainUiLogUpdater("滚动到下一页...");
                    if (!xhsActions.scrollSearchResultsPage()) {
                        utils.log("MAIN_V3_COMMENTING: Failed to scroll or already at bottom. Ending task.");
                        mainUiLogUpdater("无法滚动或已达底部，任务结束。");
                        break;
                    }
                    updateScrapingStatusDisplay("已滚动,等待加载...");
                    sleep(2500 + Math.random() * 1500); // Wait for new content to load
                } // End while isCommentingNotes
            } catch (e) {
                utils.logError("MAIN_V3_COMMENTING: Error in note commenting thread: " + e.toString() + "\nStack: " + e.stack);
                mainUiLogUpdater("笔记评论线程错误: " + e.toString().substring(0, 50));
            } finally {
                isCommentingNotes = false;
                utils.log("MAIN_V3_COMMENTING: Note commenting thread finished. Total notes commented: " + notesCommentedThisSession);
                mainUiLogUpdater("笔记评论任务结束。评论笔记数: " + notesCommentedThisSession);
                updateScrapingStatusDisplay("笔记评论结束");
                toast("笔记评论任务已结束。");
            }
        }); // 结束 threads.start
    }, // 结束 mainTaskManager.startNoteCommenting 函数
}; // 结束 mainTaskManager 对象

// 5. Set uiManager in utils
try {
    utils.setUiManager({
        updateLog: mainUiLogUpdater
        // Add other UI update functions if utils needs to call them directly
        // For comment scraping, main.js calls uiModule's functions directly via wrappers.
    });
    utils.log("MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.");
} catch (e_set_uim) {
    let setUiManagerErrMsg = "MAIN_V3: ERROR setting UI manager in utils: " + e_set_uim.toString();
    utils.log(setUiManagerErrMsg); console.error(setUiManagerErrMsg); console.error(e_set_uim.stack); toast(setUiManagerErrMsg);
}

// 6. Create UI Layout
try {
    utils.log("MAIN_V3: Getting UI XML from uiModule...");
    const uiXmlString = uiModule.getUILayoutXML();
    if (typeof uiXmlString !== 'string' || uiXmlString.trim() === "") {
        throw new Error("getUILayoutXML did not return a valid XML string.");
    }
    utils.log("MAIN_V3: UI XML string received. Calling ui.layout()...");
    ui.layout(uiXmlString);
    globalUIObject = ui; // Store the global ui object
    utils.log("MAIN_V3: ui.layout() call completed. globalUIObject populated.");
    toast("MAIN_V3: UI Layout Initialized!");
} catch (e_layout) {
    let layoutErrMsg = "MAIN_V3: FATAL ERROR during ui.layout(): " + e_layout.toString();
    utils.log(layoutErrMsg); console.error(layoutErrMsg); console.error(e_layout.stack); toast(layoutErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + layoutErrMsg + "\nStack: " + e_layout.stack + "\n"); } catch (fe) { }
    throw e_layout;
}

// 7. Attach Event Handlers
try {
    utils.log("MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...");
    uiModule.attachUIEventHandlers(globalUIObject, utils, dataManager, configManager, mainTaskManager);
    utils.log("MAIN_V3: uiModule.attachUIEventHandlers() call completed.");
} catch (e_events) {
    let eventsErrMsg = "MAIN_V3: FATAL ERROR during attachUIEventHandlers(): " + e_events.toString();
    utils.log(eventsErrMsg); console.error(eventsErrMsg); console.error(e_events.stack); toast(eventsErrMsg);
    try { files.append(files.join(files.getSdcardPath(), "XHS_Critical_Error_MainV3.txt"), new Date().toLocaleString() + ": " + eventsErrMsg + "\nStack: " + e_events.stack + "\n"); } catch (fe) { }
    throw e_events;
}

// Initial UI state for comment scraping
updateScrapingStatusDisplay("未开始");
updateProcessedNotesCountDisplay(0);
// Load total previously collected users for initial display, if desired
// const totalPreviouslyScraped = dataManager.getScrapedCommentUsers().length;
// updateCollectedUsersCountDisplay(totalPreviouslyScraped);
updateCollectedUsersCountDisplay(0); // Or start fresh for "session" count

utils.log("MAIN_V3: Script fully initialized. UI should be active.");

// 启动时提示用户关闭小红书
setTimeout(function () {
    toast("重要提示：请先手动关闭小红书app，确保从干净状态开始任务！");
    utils.log("MAIN_V3: 已提示用户关闭小红书app");
}, 2000);