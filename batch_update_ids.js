/**
 * 批量更新元素ID工具
 * 根据新的元素ID清单批量更新所有相关文件
 */

var utils = require('./utils.js');

// 元素ID映射表（从元素ID清单.md中提取）
var ID_MAPPING = {
    // 搜索相关
    "com.xingin.xhs:id/fce": "com.xingin.xhs:id/gcs",           // 搜索按钮
    "com.xingin.xhs:id/ch9": "com.xingin.xhs:id/j16",           // 筛选按钮/页面指示器
    "com.xingin.xhs:id/hpx": "com.xingin.xhs:id/j15",           // 笔记卡片容器
    "com.xingin.xhs:id/g_q": "com.xingin.xhs:id/hcl",           // 笔记卡片标题
    "com.xingin.xhs:id/zb": "com.xingin.xhs:id/a1e",            // 笔记卡片作者

    // 图文笔记详情页
    "com.xingin.xhs:id/a0r": "com.xingin.xhs:id/a2z",           // 页面指示器/作者头像
    "com.xingin.xhs:id/gn_": "com.xingin.xhs:id/eho",           // 笔记类型指示器
    "com.xingin.xhs:id/nickNameTV": "com.xingin.xhs:id/nickNameTV", // 作者昵称（未变）
    "com.xingin.xhs:id/g_s": "com.xingin.xhs:id/hcn",           // 笔记标题
    "com.xingin.xhs:id/drg": "com.xingin.xhs:id/eio",           // 笔记内容
    "com.xingin.xhs:id/g9w": "com.xingin.xhs:id/hbq",           // 点赞按钮
    "com.xingin.xhs:id/g88": "com.xingin.xhs:id/h_z",           // 收藏按钮
    "com.xingin.xhs:id/dwu": "com.xingin.xhs:id/ha5",           // 评论按钮
    "com.xingin.xhs:id/iw2": "com.xingin.xhs:id/kc0",           // 顶部容器
    "com.xingin.xhs:id/moreOperateIV": "com.xingin.xhs:id/moreOperateIV", // 更多操作按钮（未变）

    // 视频笔记详情页
    "com.xingin.xhs:id/matrixnickNameView": "com.xingin.xhs:id/matrixnickNameView", // 作者昵称（未变）
    "com.xingin.xhs:id/wm": "com.xingin.xhs:id/yf",             // 主容器
    "com.xingin.xhs:id/c9y": "com.xingin.xhs:id/cm1",           // 功能按钮组件
    "com.xingin.xhs:id/gg9": "com.xingin.xhs:id/hjo",           // 分享菜单容器
    "com.xingin.xhs:id/hzs": "com.xingin.xhs:id/jb0",           // 复制链接按钮

    // 评论系统
    "com.xingin.xhs:id/g8b": "com.xingin.xhs:id/ha3",           // 评论数量指示器
    "com.xingin.xhs:id/g98": "com.xingin.xhs:id/hb2",           // 评论区容器
    "com.xingin.xhs:id/eud": "com.xingin.xhs:id/fs9",           // 评论项根元素
    "com.xingin.xhs:id/gks": "com.xingin.xhs:id/hp2",           // 评论内容容器
    "com.xingin.xhs:id/bhd": "com.xingin.xhs:id/bgh",           // 评论父容器
    "com.xingin.xhs:id/jmt": "com.xingin.xhs:id/I98",           // 评论者昵称
    "com.xingin.xhs:id/jfh": "com.xingin.xhs:id/kym",           // 评论内容
    "com.xingin.xhs:id/e_d": "com.xingin.xhs:id/f4k",           // 评论者头像
    "com.xingin.xhs:id/euy": "com.xingin.xhs:id/fsz",           // 评论点赞按钮
    "com.xingin.xhs:id/f29": "com.xingin.xhs:id/g1k",           // 评论点赞图标
    "com.xingin.xhs:id/jia": "com.xingin.xhs:id/I33",           // 评论点赞数
    "com.xingin.xhs:id/ie1": "com.xingin.xhs:id/eol",           // 回复容器

    // 导航元素
    "com.xingin.xhs:id/a2q": "com.xingin.xhs:id/a54"            // 返回按钮
};

// 需要更新的文件列表
var FILES_TO_UPDATE = [
    "xhs_actions.js",
    "xhs_comment_actions.js",
    "xhs_simple_comments.js",
    "xhs_video_comments.js",
    "xhs_note_commenting.js",
    "xhs_share_link.js",        // 分享链接复制功能
    "xhs_note_types.js",        // 笔记类型检测
];

/**
 * 更新单个文件中的元素ID
 */
function updateFileIds(filename) {
    utils.log("BATCH_UPDATE: 开始更新文件: " + filename);

    try {
        // 读取文件内容
        var content = files.read(filename);
        if (!content) {
            utils.log("BATCH_UPDATE: 无法读取文件: " + filename);
            return false;
        }

        var originalContent = content;
        var updateCount = 0;

        // 遍历所有ID映射，进行替换
        for (var oldId in ID_MAPPING) {
            var newId = ID_MAPPING[oldId];

            // 跳过未变化的ID
            if (oldId === newId) {
                continue;
            }

            // 使用全局替换
            var regex = new RegExp(escapeRegExp(oldId), 'g');
            var beforeReplace = content;
            content = content.replace(regex, newId);

            // 统计替换次数
            var matches = beforeReplace.match(regex);
            if (matches) {
                updateCount += matches.length;
                utils.log("BATCH_UPDATE: 替换 " + oldId + " -> " + newId + " (" + matches.length + "次)");
            }
        }

        // 如果有更新，写回文件
        if (content !== originalContent) {
            // 备份原文件
            var backupFilename = filename + ".backup." + new Date().toISOString().replace(/[:.]/g, '-');
            files.write(backupFilename, originalContent);
            utils.log("BATCH_UPDATE: 原文件已备份到: " + backupFilename);

            // 写入更新后的内容
            files.write(filename, content);
            utils.log("BATCH_UPDATE: ✅ 文件更新完成: " + filename + " (共" + updateCount + "处更新)");
            return true;
        } else {
            utils.log("BATCH_UPDATE: 文件无需更新: " + filename);
            return true;
        }

    } catch (e) {
        utils.log("BATCH_UPDATE: 更新文件失败: " + filename + " - " + e.toString());
        return false;
    }
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 批量更新所有文件
 */
function batchUpdateAllFiles() {
    utils.log("BATCH_UPDATE: === 开始批量更新元素ID ===");

    var successCount = 0;
    var totalCount = FILES_TO_UPDATE.length;

    // 显示更新计划
    utils.log("BATCH_UPDATE: 计划更新 " + totalCount + " 个文件");
    utils.log("BATCH_UPDATE: 计划替换 " + Object.keys(ID_MAPPING).length + " 个元素ID");

    // 逐个更新文件
    for (var i = 0; i < FILES_TO_UPDATE.length; i++) {
        var filename = FILES_TO_UPDATE[i];

        // 检查文件是否存在
        if (!files.exists(filename)) {
            utils.log("BATCH_UPDATE: ⚠️ 文件不存在，跳过: " + filename);
            continue;
        }

        if (updateFileIds(filename)) {
            successCount++;
        }
    }

    utils.log("BATCH_UPDATE: === 批量更新完成 ===");
    utils.log("BATCH_UPDATE: 成功更新: " + successCount + "/" + totalCount + " 个文件");

    return successCount === totalCount;
}

/**
 * 生成更新报告
 */
function generateUpdateReport() {
    var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    var reportFilename = "id_update_report_" + timestamp + ".txt";

    var report = "=== 元素ID批量更新报告 ===\n";
    report += "更新时间: " + new Date().toISOString() + "\n";
    report += "设备信息: " + device.width + "x" + device.height + "\n\n";

    report += "📋 ID映射表:\n\n";
    for (var oldId in ID_MAPPING) {
        var newId = ID_MAPPING[oldId];
        if (oldId !== newId) {
            report += "✅ " + oldId + "\n";
            report += "   -> " + newId + "\n\n";
        } else {
            report += "⚪ " + oldId + " (未变化)\n\n";
        }
    }

    report += "📁 更新的文件:\n\n";
    for (var i = 0; i < FILES_TO_UPDATE.length; i++) {
        report += "- " + FILES_TO_UPDATE[i] + "\n";
    }

    report += "\n💡 注意事项:\n";
    report += "1. 原文件已自动备份（.backup.时间戳）\n";
    report += "2. 如有问题可从备份文件恢复\n";
    report += "3. 建议测试所有功能确保正常工作\n";
    report += "4. 未变化的ID表示新旧版本使用相同ID\n";

    try {
        files.write(reportFilename, report);
        utils.log("BATCH_UPDATE: ✅ 更新报告已保存: " + reportFilename);
    } catch (e) {
        utils.log("BATCH_UPDATE: 保存更新报告失败: " + e.toString());
    }
}

/**
 * 验证更新结果
 */
function validateUpdate() {
    utils.log("BATCH_UPDATE: 开始验证更新结果...");

    var validationResults = {};

    for (var i = 0; i < FILES_TO_UPDATE.length; i++) {
        var filename = FILES_TO_UPDATE[i];

        if (!files.exists(filename)) {
            continue;
        }

        try {
            var content = files.read(filename);
            var oldIdCount = 0;
            var newIdCount = 0;

            // 统计旧ID和新ID的出现次数
            for (var oldId in ID_MAPPING) {
                var newId = ID_MAPPING[oldId];

                if (oldId !== newId) {
                    var oldMatches = content.match(new RegExp(escapeRegExp(oldId), 'g'));
                    var newMatches = content.match(new RegExp(escapeRegExp(newId), 'g'));

                    if (oldMatches) oldIdCount += oldMatches.length;
                    if (newMatches) newIdCount += newMatches.length;
                }
            }

            validationResults[filename] = {
                oldIdCount: oldIdCount,
                newIdCount: newIdCount,
                success: oldIdCount === 0
            };

            if (oldIdCount === 0) {
                utils.log("BATCH_UPDATE: ✅ " + filename + " - 所有旧ID已更新");
            } else {
                utils.log("BATCH_UPDATE: ⚠️ " + filename + " - 仍有 " + oldIdCount + " 个旧ID未更新");
            }

        } catch (e) {
            utils.log("BATCH_UPDATE: 验证文件失败: " + filename + " - " + e.toString());
            validationResults[filename] = { success: false, error: e.toString() };
        }
    }

    return validationResults;
}

/**
 * 主函数
 */
function main() {
    var confirmUpdate = dialogs.confirm("批量更新元素ID",
        "即将批量更新以下文件中的元素ID：\n\n" +
        FILES_TO_UPDATE.join("\n") + "\n\n" +
        "更新内容：\n" +
        "- 共 " + Object.keys(ID_MAPPING).length + " 个元素ID映射\n" +
        "- 自动备份原文件\n" +
        "- 生成更新报告\n\n" +
        "是否继续？"
    );

    if (!confirmUpdate) {
        utils.log("BATCH_UPDATE: 用户取消更新");
        return;
    }

    // 执行批量更新
    var updateSuccess = batchUpdateAllFiles();

    // 生成更新报告
    generateUpdateReport();

    // 验证更新结果
    var validationResults = validateUpdate();

    // 显示结果
    var message = updateSuccess ? "批量更新完成！" : "批量更新部分失败！";
    message += "\n\n请查看：";
    message += "\n1. 控制台日志了解详细过程";
    message += "\n2. 更新报告文件了解具体更改";
    message += "\n3. 备份文件（如需恢复）";
    message += "\n\n建议接下来测试所有功能确保正常工作。";

    dialogs.alert("更新完成", message);
}

// 如果直接运行此文件，执行主函数
if (typeof module === 'undefined') {
    main();
}

module.exports = {
    batchUpdateAllFiles,
    updateFileIds,
    generateUpdateReport,
    validateUpdate,
    ID_MAPPING
};
