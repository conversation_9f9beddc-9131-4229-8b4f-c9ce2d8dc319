# 小红书自动化项目 - 完整元素ID清单

## 🔍 搜索相关元素

### 搜索页面
- **搜索按钮**: `com.xingin.xhs:id/fce` (Button, text="搜索")      com.xingin.xhs:id/gcs
- **筛选按钮**: `com.xingin.xhs:id/ch9` (text="筛选")              com.xingin.xhs:id/j16
- **筛选按钮备用**: `com.xingin.xhs:id/hpy` (text="筛选")

### 搜索结果页面
- **页面指示器**: `com.xingin.xhs:id/ch9` (text="筛选")             com.xingin.xhs:id/j16
- **笔记卡片容器**: `com.xingin.xhs:id/hpx` (FrameLayout)           com.xingin.xhs:id/j15
- **笔记卡片标题**: `com.xingin.xhs:id/g_q` (TextView)              com.xingin.xhs:id/hcl
- **笔记卡片作者**: `com.xingin.xhs:id/zb` (TextView)               com.xingin.xhs:id/a1e

## 📝 图文笔记详情页元素

### 页面识别
- **页面指示器**: `com.xingin.xhs:id/a0r` (FrameLayout) - 作者头像     com.xingin.xhs:id/a2z
- **笔记类型指示器**: `com.xingin.xhs:id/gn_` - 图文笔记特有            com.xingin.xhs:id/eho

### 作者信息
- **作者头像**: `com.xingin.xhs:id/a0r` (FrameLayout)                  com.xingin.xhs:id/a2z
- **作者昵称**: `com.xingin.xhs:id/nickNameTV` (TextView)              com.xingin.xhs:id/nickNameTV

### 笔记内容
- **笔记标题**: `com.xingin.xhs:id/g_s` (TextView)                     com.xingin.xhs:id/hcn
- **笔记内容**: `com.xingin.xhs:id/drg` (TextView)                     com.xingin.xhs:id/eio

### 操作按钮
- **点赞按钮**: `com.xingin.xhs:id/g9w` (ImageView)                    com.xingin.xhs:id/hbq
- **收藏按钮**: `com.xingin.xhs:id/g88` (Button)                       com.xingin.xhs:id/h_z
- **评论按钮**: `com.xingin.xhs:id/dwu` (TextView)                     com.xingin.xhs:id/ha5

### 分享相关
- **顶部容器**: `com.xingin.xhs:id/iw2`                                com.xingin.xhs:id/kc0
- **更多操作按钮**: `com.xingin.xhs:id/moreOperateIV`                  com.xingin.xhs:id/moreOperateIV

## 🎥 视频笔记详情页元素

### 作者信息
- **作者头像**: `com.xingin.xhs:id/jx2` (ImageView)                    
- **作者昵称**: `com.xingin.xhs:id/matrixnickNameView` (TextView)      com.xingin.xhs:id/matrixnickNameView

### 视频容器
- **主容器**: `com.xingin.xhs:id/wm`                                   com.xingin.xhs:id/yf
- **功能按钮组件**: `com.xingin.xhs:id/c9y`                            com.xingin.xhs:id/cm1

### 分享菜单
- **分享菜单容器**: `com.xingin.xhs:id/gg9`                            com.xingin.xhs:id/hjo
- **复制链接按钮**: `com.xingin.xhs:id/hzs` (text="复制链接")           com.xingin.xhs:id/jb0

## 💬 评论系统元素

### 评论区标识
- **评论数量指示器**: `com.xingin.xhs:id/g8b` (TextView, "共X条评论")   com.xingin.xhs:id/ha3
- **评论区容器**: `com.xingin.xhs:id/g98` (RecyclerView)               com.xingin.xhs:id/hb2

### 单条评论元素
- **评论项根元素**: `com.xingin.xhs:id/eud` (LinearLayout)             com.xingin.xhs:id/fs9
- **评论内容容器**: `com.xingin.xhs:id/gks` (LinearLayout)             com.xingin.xhs:id/hp2
- **评论父容器**: `com.xingin.xhs:id/bhd` (LinearLayout)               com.xingin.xhs:id/bgh
- **评论者昵称**: `com.xingin.xhs:id/jmt` (TextView)                   com.xingin.xhs:id/I98
- **评论内容**: `com.xingin.xhs:id/jfh` (TextView)                     com.xingin.xhs:id/kym
- **评论者头像**: `com.xingin.xhs:id/e_d` (View)                       com.xingin.xhs:id/f4k

### 评论操作
- **评论点赞按钮**: `com.xingin.xhs:id/euy` (Button)                   com.xingin.xhs:id/fsz
- **评论点赞图标**: `com.xingin.xhs:id/f29` (ImageView)                com.xingin.xhs:id/g1k
- **评论点赞数**: `com.xingin.xhs:id/jia` (TextView)                   com.xingin.xhs:id/

### 评论加载状态
- **加载更多评论**: `com.xingin.xhs:id/evn` (TextView)
- **评论结束指示器**: text="- 到底了 -"
- **单条评论容器**: `com.xingin.xhs:id/eud`
- **回复容器**: `com.xingin.xhs:id/ie1`

## 🔙 导航元素

### 返回按钮
- **标准返回按钮**: `com.xingin.xhs:id/a2q` (desc="返回")

## 📊 其他功能元素

### 搜索按钮备用策略
- **ID包含search_btn**: idContains("search_btn")
- **ID包含search_confirm_button**: idContains("search_confirm_button")
- **ID包含search_text_layout**: idContains("search_text_layout")
- **ID包含txt_search**: idContains("txt_search")
- **ID包含search_submit**: idContains("search_submit")
- **ID包含confirm**: idContains("confirm")
- **ID包含submit**: idContains("submit")

## 🎯 关键元素优先级

### 高优先级（必须适配）
1. **搜索结果页识别**: `com.xingin.xhs:id/ch9` (筛选按钮)
2. **笔记卡片**: `com.xingin.xhs:id/hpx`
3. **笔记标题**: `com.xingin.xhs:id/g_q`
4. **笔记作者**: `com.xingin.xhs:id/zb`
5. **图文笔记识别**: `com.xingin.xhs:id/a0r`
6. **作者昵称**: `com.xingin.xhs:id/nickNameTV`
7. **评论者昵称**: `com.xingin.xhs:id/jmt`
8. **评论内容**: `com.xingin.xhs:id/jfh`
9. **返回按钮**: `com.xingin.xhs:id/a2q`

### 中优先级（影响功能）
1. **笔记标题**: `com.xingin.xhs:id/g_s`
2. **笔记内容**: `com.xingin.xhs:id/drg`
3. **评论按钮**: `com.xingin.xhs:id/dwu`
4. **点赞按钮**: `com.xingin.xhs:id/g9w`
5. **评论点赞**: `com.xingin.xhs:id/euy`
6. **视频容器**: `com.xingin.xhs:id/wm`
7. **视频功能按钮**: `com.xingin.xhs:id/c9y`

### 低优先级（增强功能）
1. **收藏按钮**: `com.xingin.xhs:id/g88`
2. **分享相关**: `com.xingin.xhs:id/iw2`, `com.xingin.xhs:id/moreOperateIV`
3. **视频分享**: `com.xingin.xhs:id/gg9`, `com.xingin.xhs:id/hzs`
4. **评论加载**: `com.xingin.xhs:id/evn`

## 📝 适配建议

### 第一阶段：核心功能
请优先查找并提供以下9个高优先级元素的新ID：
1. `com.xingin.xhs:id/ch9` - 筛选按钮
2. `com.xingin.xhs:id/hpx` - 笔记卡片
3. `com.xingin.xhs:id/g_q` - 笔记标题
4. `com.xingin.xhs:id/zb` - 笔记作者
5. `com.xingin.xhs:id/a0r` - 图文笔记识别
6. `com.xingin.xhs:id/nickNameTV` - 作者昵称
7. `com.xingin.xhs:id/jmt` - 评论者昵称
8. `com.xingin.xhs:id/jfh` - 评论内容
9. `com.xingin.xhs:id/a2q` - 返回按钮

### 第二阶段：扩展功能
完成核心功能后，再查找中优先级和低优先级元素。

## 🔍 查找方法建议

1. **使用Auto.js的布局分析工具**
2. **在对应页面使用无障碍服务查看元素**
3. **使用开发者工具或UI自动化工具**
4. **对比新旧版本的界面差异**

请按照优先级顺序，逐个查找这些元素的新ID，然后提供给我进行批量更新。
