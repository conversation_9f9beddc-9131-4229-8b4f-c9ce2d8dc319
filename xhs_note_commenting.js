/**
 * XHS Note Commenting Module
 * Support for both image/text and video note commenting
 */

var utils = require('./utils.js');
var configManager = require('./config.js');
var noteTypes = require('./xhs_note_types.js');

/**
 * 简化的笔记内容提取函数
 * @returns {string|null} 提取到的文本内容，失败时返回null
 */
function extractNoteContentSimple() {
    utils.log("NOTE_COMMENT: 开始提取当前笔记内容...");
    let extractedText = null;

    try {
        // 尝试图文笔记内容提取
        const noteContentContainer = id("com.xingin.xhs:id/drg").findOne(1500);
        if (noteContentContainer) {
            utils.log("NOTE_COMMENT: 找到图文笔记容器");
            const textViews = noteContentContainer.find(className("android.widget.TextView"));
            if (textViews && !textViews.empty()) {
                let texts = [];
                textViews.forEach(tv => {
                    if (tv && tv.text()) {
                        texts.push(tv.text());
                    }
                });
                if (texts.length > 0) {
                    extractedText = texts.join("\n").trim();
                    utils.log("NOTE_COMMENT: 图文内容提取成功，长度: " + extractedText.length);
                }
            }
        }

        // 如果图文提取未成功，尝试视频笔记简介提取
        if (!extractedText || extractedText.trim() === "") {
            utils.log("NOTE_COMMENT: 尝试视频笔记简介提取...");
            const videoDescriptionElement = id("com.xingin.xhs:id/ba8").findOne(1000);
            if (videoDescriptionElement && videoDescriptionElement.text()) {
                extractedText = videoDescriptionElement.text().trim();
                utils.log("NOTE_COMMENT: 视频简介提取成功，长度: " + extractedText.length);
            }
        }

        if (extractedText && extractedText.trim() === "") {
            extractedText = null;
        }

    } catch (e) {
        utils.log("NOTE_COMMENT: 提取笔记内容时发生异常: " + e.toString());
        extractedText = null;
    }

    return extractedText;
}

/**
 * Comment Manager Constructor Function
 */
function CommentManager(comments, mode) {
    // 处理不同类型的输入
    if (Array.isArray(comments)) {
        // 如果是数组，直接使用
        this.comments = comments;
    } else if (typeof comments === 'string') {
        // 如果是字符串，按换行符分割
        this.comments = comments.split('\n')
            .map(function (comment) { return comment.trim(); })
            .filter(function (comment) { return comment.length > 0; });
    } else {
        this.comments = [];
    }

    this.mode = mode || 'random';
    this.currentIndex = 0;

    utils.log("COMMENT_MANAGER: Initialized with " + this.comments.length + " comments, mode: " + this.mode);
    if (this.comments.length > 0) {
        utils.log("COMMENT_MANAGER: Comments: " + JSON.stringify(this.comments));
    }
}

CommentManager.prototype.getCommentCount = function () {
    return this.comments.length;
};

CommentManager.prototype.getNextComment = function () {
    if (this.comments.length === 0) {
        return null;
    }

    var comment;
    if (this.mode === 'random') {
        var randomIndex = Math.floor(Math.random() * this.comments.length);
        comment = this.comments[randomIndex];
        utils.log("COMMENT_MANAGER: Random comment #" + (randomIndex + 1) + ": \"" + comment + "\"");
    } else {
        comment = this.comments[this.currentIndex];
        utils.log("COMMENT_MANAGER: Sequential comment #" + (this.currentIndex + 1) + ": \"" + comment + "\"");
        this.currentIndex = (this.currentIndex + 1) % this.comments.length;
    }

    return comment;
};

// 创建持久化存储用于评论去重
var commentedNotesStorage = storages.create("xhs_commented_notes");

/**
 * Comment Deduplication Manager Constructor Function
 */
function CommentDeduplicationManager() {
    this.storage = commentedNotesStorage;
}

CommentDeduplicationManager.prototype.generateNoteId = function (noteTitle, noteAuthor) {
    var rawId = (noteAuthor || "Unknown") + "::" + (noteTitle || "Untitled");
    var hash = 0;
    for (var i = 0; i < rawId.length; i++) {
        var char = rawId.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    var noteId = "note_" + Math.abs(hash) + "_" + rawId.length;
    return noteId;
};

CommentDeduplicationManager.prototype.isNoteCommented = function (noteTitle, noteAuthor) {
    var noteId = this.generateNoteId(noteTitle, noteAuthor);
    var result = this.storage.get(noteId, false);

    // 修复：正确处理返回值
    var isCommented = false;
    if (result) {
        if (typeof result === 'boolean') {
            isCommented = result;
        } else if (typeof result === 'object' && result.commented) {
            isCommented = result.commented;
        } else {
            // 如果存在任何非false值，都认为已评论
            isCommented = true;
        }
    }

    utils.log("DEDUP: 检查笔记是否已评论: \"" + noteId + "\" -> " + isCommented + " (原始值: " + result + ")");
    return isCommented;
};

CommentDeduplicationManager.prototype.markNoteAsCommented = function (noteTitle, noteAuthor) {
    var noteId = this.generateNoteId(noteTitle, noteAuthor);
    var timestamp = new Date().toISOString();
    this.storage.put(noteId, {
        commented: true,
        timestamp: timestamp,
        title: noteTitle,
        author: noteAuthor
    });
    utils.log("DEDUP: 标记笔记为已评论: \"" + noteId + "\" at " + timestamp);
};

CommentDeduplicationManager.prototype.getCommentedNotesCount = function () {
    try {
        var keys = this.storage.keys();
        if (keys && typeof keys.length !== 'undefined') {
            return keys.length;
        } else if (keys && typeof keys === 'object') {
            // 如果keys是对象但没有length属性，尝试转换为数组
            var count = 0;
            for (var key in keys) {
                if (keys.hasOwnProperty(key)) {
                    count++;
                }
            }
            return count;
        } else {
            return 0;
        }
    } catch (error) {
        utils.log("DEDUP: Error getting commented notes count: " + error);
        return 0;
    }
};

// 添加清理去重数据的方法
CommentDeduplicationManager.prototype.clearAllCommentedNotes = function () {
    try {
        this.storage.clear();
        utils.log("DEDUP: 已清理所有去重数据");
        return true;
    } catch (error) {
        utils.log("DEDUP: 清理去重数据失败: " + error);
        return false;
    }
};

// 添加清理指定笔记的去重记录
CommentDeduplicationManager.prototype.removeNoteFromCommented = function (noteTitle, noteAuthor) {
    try {
        var noteId = this.generateNoteId(noteTitle, noteAuthor);
        this.storage.remove(noteId);
        utils.log("DEDUP: 已移除笔记的去重记录: \"" + noteId + "\"");
        return true;
    } catch (error) {
        utils.log("DEDUP: 移除笔记去重记录失败: " + error);
        return false;
    }
};

/**
 * Publish comment in image/text note - 完整版本包含所有调试和备用方案
 */
function publishImageTextNoteComment(commentText) {
    try {
        utils.log("IMAGE_TEXT_COMMENT: Starting to publish comment: \"" + commentText + "\"");

        // 第一步：查找评论容器
        var commentContainer = id("com.xingin.xhs:id/cm1").findOne(3000);
        if (!commentContainer) {
            utils.log("IMAGE_TEXT_COMMENT: Comment container not found (cm1)");
            return false;
        }

        // 第二步：点击评论触发器 (ha5)
        var commentTrigger = commentContainer.findOne(id("com.xingin.xhs:id/ha5"));
        if (!commentTrigger) {
            utils.log("IMAGE_TEXT_COMMENT: Comment trigger not found (ha5)");
            return false;
        }

        utils.log("IMAGE_TEXT_COMMENT: Clicking comment trigger (ha5)");
        if (!commentTrigger.click()) {
            utils.log("IMAGE_TEXT_COMMENT: Failed to click comment trigger");
            return false;
        }

        sleep(2000);

        // 第三步：点击 eol 元素（图文笔记特有步骤）
        var eolElement = id("com.xingin.xhs:id/eol").findOne(3000);
        if (!eolElement) {
            utils.log("IMAGE_TEXT_COMMENT: eol element not found");
            return false;
        }

        utils.log("IMAGE_TEXT_COMMENT: Clicking eol element");
        if (!eolElement.click()) {
            utils.log("IMAGE_TEXT_COMMENT: Failed to click eol element");
            return false;
        }

        sleep(2000);

        // 第四步：点击评论输入框 (g41)
        utils.log("IMAGE_TEXT_COMMENT: Looking for comment input (g41)");
        var commentInput = id("com.xingin.xhs:id/g41").findOne(3000);
        if (!commentInput) {
            utils.log("IMAGE_TEXT_COMMENT: Comment input not found (g41)");
            return false;
        }

        utils.log("IMAGE_TEXT_COMMENT: Clicking comment input (g41)");
        if (!commentInput.click()) {
            utils.log("IMAGE_TEXT_COMMENT: Failed to click comment input");
            return false;
        }

        // 第五步：输入评论内容
        utils.log("IMAGE_TEXT_COMMENT: Inputting comment text");
        if (!commentInput.setText(commentText)) {
            utils.log("IMAGE_TEXT_COMMENT: Failed to input comment text");
            return false;
        }

        sleep(1000);

        // 第六步：查找并点击发送按钮
        utils.log("IMAGE_TEXT_COMMENT: Looking for send button (fcs)");
        var sendButton = id("com.xingin.xhs:id/fcs").findOne(3000);
        if (!sendButton) {
            utils.log("IMAGE_TEXT_COMMENT: Send button not found (fcs)");
            return false;
        }

        utils.log("IMAGE_TEXT_COMMENT: Clicking send button");
        if (!sendButton.click()) {
            utils.log("IMAGE_TEXT_COMMENT: Failed to click send button");
            return false;
        }

        sleep(2000);
        utils.log("IMAGE_TEXT_COMMENT: Comment published successfully");
        return true;

    } catch (error) {
        utils.log("IMAGE_TEXT_COMMENT: Exception during comment publishing: " + error);
        return false;
    }
}

/**
 * Publish comment in video note
 */
function publishVideoNoteComment(commentText) {
    try {
        utils.log("VIDEO_COMMENT: Starting to publish comment: \"" + commentText + "\"");

        var videoContainer = id("com.xingin.xhs:id/cm1").findOne(3000);
        if (!videoContainer) {
            utils.log("VIDEO_COMMENT: Video container not found (cm1)");
            return false;
        }

        utils.log("VIDEO_COMMENT: Found video container, looking for comment button...");
        var commentButton = videoContainer.child(1);
        if (!commentButton) {
            utils.log("VIDEO_COMMENT: Comment button not found (indexInParent=1)");
            return false;
        }

        utils.log("VIDEO_COMMENT: Clicking comment button to open comment popup...");
        if (!commentButton.click()) {
            utils.log("VIDEO_COMMENT: Failed to click comment button");
            return false;
        }

        sleep(2000);

        utils.log("VIDEO_COMMENT: Looking for comment input in popup...");

        var commentLayout = id("com.xingin.xhs:id/commentLayout").findOne(3000);
        if (!commentLayout) {
            utils.log("VIDEO_COMMENT: Comment popup container not found (commentLayout)");
            return false;
        }

        // 查找评论输入框（使用新ID）
        var commentInput = null;

        // 方法1: 使用新的输入框ID
        commentInput = commentLayout.findOne(id("com.xingin.xhs:id/g41"));
        if (commentInput) {
            utils.log("VIDEO_COMMENT: Found comment input with new ID (g41)");
        }

        // 方法2: 使用旧的输入框ID作为备用
        if (!commentInput) {
            commentInput = commentLayout.findOne(id("com.xingin.xhs:id/f4m"));
            if (commentInput) {
                utils.log("VIDEO_COMMENT: Found comment input with old ID (f4m)");
            }
        }

        // 方法3: 直接全局查找
        if (!commentInput) {
            commentInput = id("com.xingin.xhs:id/g41").findOne(2000);
            if (!commentInput) {
                commentInput = id("com.xingin.xhs:id/f4m").findOne(2000);
            }
        }

        if (!commentInput) {
            utils.log("VIDEO_COMMENT: Comment input not found in popup (f4m)");

            // 添加详细调试信息来找出新的评论输入框ID
            utils.log("VIDEO_COMMENT: 调试 - 查找视频评论弹窗中的所有输入元素...");

            // 查找所有EditText元素
            var allEditTexts = commentLayout.find(className("android.widget.EditText"));
            utils.log("VIDEO_COMMENT: 在评论弹窗中找到 " + (allEditTexts ? allEditTexts.length : 0) + " 个EditText元素");

            if (allEditTexts && allEditTexts.length > 0) {
                for (var i = 0; i < allEditTexts.length; i++) {
                    var editText = allEditTexts[i];
                    utils.log("VIDEO_COMMENT: EditText[" + i + "] - ID: " + editText.id() +
                        ", 可见: " + editText.visibleToUser() +
                        ", 可点击: " + editText.clickable() +
                        ", 文本: '" + (editText.text() || '') + "'");
                }
            }

            // 查找所有可能的输入相关元素
            var allInputElements = commentLayout.find(className("android.view.View"));
            utils.log("VIDEO_COMMENT: 在评论弹窗中找到 " + (allInputElements ? allInputElements.length : 0) + " 个View元素");

            // 查找包含"评论"或"输入"文字的元素
            var inputRelatedElements = commentLayout.find(textMatches(".*评论.*|.*输入.*|.*说点什么.*"));
            utils.log("VIDEO_COMMENT: 找到 " + (inputRelatedElements ? inputRelatedElements.length : 0) + " 个包含输入相关文字的元素");

            if (inputRelatedElements && inputRelatedElements.length > 0) {
                for (var j = 0; j < Math.min(inputRelatedElements.length, 3); j++) {
                    var elem = inputRelatedElements[j];
                    utils.log("VIDEO_COMMENT: 输入相关元素[" + j + "] - ID: " + elem.id() +
                        ", 类名: " + elem.className() +
                        ", 文本: '" + (elem.text() || '') + "'");
                }
            }

            return false;
        }

        utils.log("VIDEO_COMMENT: Found comment input, type: " + commentInput.className());

        // 正确的评论流程：先点击激活输入框，再输入内容
        utils.log("VIDEO_COMMENT: Clicking input to activate comment mode...");
        if (!commentInput.click()) {
            utils.log("VIDEO_COMMENT: Failed to click comment input");
            return false;
        }

        sleep(1000); // 等待输入框激活和发送按钮出现

        // 重新查找激活后的输入框元素
        utils.log("VIDEO_COMMENT: Re-finding activated input element...");
        var activatedInput = null;

        // 方法1: 在commentLayout中查找激活后的输入框
        activatedInput = commentLayout.findOne(className("android.widget.EditText"));
        if (activatedInput) {
            utils.log("VIDEO_COMMENT: Found activated EditText in commentLayout");
        }

        // 方法2: 直接查找f4m ID
        if (!activatedInput) {
            activatedInput = commentLayout.findOne(id("f4m"));
            if (activatedInput) {
                utils.log("VIDEO_COMMENT: Found activated f4m input in commentLayout");
            }
        }

        // 方法3: 全局查找EditText
        if (!activatedInput) {
            activatedInput = className("android.widget.EditText").findOne(1000);
            if (activatedInput) {
                utils.log("VIDEO_COMMENT: Found activated EditText globally");
            }
        }

        // 如果还是没找到，使用原来的输入框
        if (!activatedInput) {
            utils.log("VIDEO_COMMENT: Using original input element");
            activatedInput = commentInput;
        }

        utils.log("VIDEO_COMMENT: Input activated, now inputting comment: \"" + commentText + "\"");
        if (!activatedInput.setText(commentText)) {
            utils.log("VIDEO_COMMENT: setText failed on activated input");
            return false;
        }

        utils.log("VIDEO_COMMENT: Comment content input successful");
        sleep(500);

        // 添加调试信息：列出当前页面所有可见的按钮
        utils.log("VIDEO_COMMENT: Debugging - listing all visible buttons on current page:");
        var allButtons = clickable(true).find();
        for (var i = 0; i < Math.min(allButtons.length, 10); i++) {
            var btn = allButtons[i];
            var btnText = btn.text() || "";
            var btnDesc = btn.desc() || "";
            var btnId = btn.id() || "";
            utils.log("VIDEO_COMMENT: Button " + i + " - Text: '" + btnText + "', Desc: '" + btnDesc + "', ID: '" + btnId + "'");
        }

        // 使用精确的发送按钮ID
        var sendButton = null;

        // 方法1: 使用发送按钮的具体ID
        sendButton = id("com.xingin.xhs:id/fcs").findOne(2000);
        if (sendButton) {
            utils.log("VIDEO_COMMENT: Found send button with ID 'fcs'");
        }

        // 方法2: 在commentLayout容器内查找发送按钮ID
        if (!sendButton) {
            sendButton = commentLayout.findOne(id("com.xingin.xhs:id/fcs"));
            if (sendButton) {
                utils.log("VIDEO_COMMENT: Found send button with ID 'fcs' in commentLayout");
            }
        }

        // 方法3: 备用方案 - 查找文本"发送"
        if (!sendButton) {
            sendButton = text("发送").findOne(1000);
            if (sendButton) {
                utils.log("VIDEO_COMMENT: Found send button with text '发送' as fallback");
            }
        }

        // 方法4: 备用方案 - 查找文本"发布"
        if (!sendButton) {
            sendButton = text("发布").findOne(1000);
            if (sendButton) {
                utils.log("VIDEO_COMMENT: Found send button with text '发布' as fallback");
            }
        }

        if (!sendButton) {
            utils.log("VIDEO_COMMENT: Send button not found with any method");
            return false;
        }

        utils.log("VIDEO_COMMENT: Found send button, clicking send...");

        // 尝试多种点击方式
        var clickSuccess = false;

        // 方法1: 普通点击
        clickSuccess = sendButton.click();
        if (clickSuccess) {
            utils.log("VIDEO_COMMENT: Send button clicked successfully with normal click");
        } else {
            utils.log("VIDEO_COMMENT: Normal click failed, trying alternative methods");

            // 方法2: 获取按钮坐标并点击
            try {
                var bounds = sendButton.bounds();
                var centerX = bounds.centerX();
                var centerY = bounds.centerY();
                utils.log("VIDEO_COMMENT: Trying coordinate click at (" + centerX + ", " + centerY + ")");
                clickSuccess = click(centerX, centerY);
                if (clickSuccess) {
                    utils.log("VIDEO_COMMENT: Send button clicked successfully with coordinate click");
                }
            } catch (e) {
                utils.log("VIDEO_COMMENT: Coordinate click failed: " + e);
            }

            // 方法3: 尝试按钮的父元素点击
            if (!clickSuccess) {
                try {
                    var parent = sendButton.parent();
                    if (parent && parent.clickable()) {
                        utils.log("VIDEO_COMMENT: Trying to click parent element");
                        clickSuccess = parent.click();
                        if (clickSuccess) {
                            utils.log("VIDEO_COMMENT: Send button clicked successfully via parent element");
                        }
                    }
                } catch (e) {
                    utils.log("VIDEO_COMMENT: Parent click failed: " + e);
                }
            }
        }

        if (!clickSuccess) {
            utils.log("VIDEO_COMMENT: Failed to click send button with all methods");
            return false;
        }

        sleep(2000);
        utils.log("VIDEO_COMMENT: Video note comment published successfully");
        return true;

    } catch (error) {
        utils.log("VIDEO_COMMENT: Exception during comment publishing: " + error);
        return false;
    }
}

/**
 * Publish comment by note type
 */
function publishCommentByNoteType(noteType, commentText) {
    if (noteType === noteTypes.NOTE_TYPES.VIDEO) {
        return publishVideoNoteComment(commentText);
    } else {
        return publishImageTextNoteComment(commentText);
    }
}

/**
 * Publish comment in current note
 * @param {string} noteTitle - 笔记标题
 * @param {string} noteAuthor - 笔记作者
 * @param {Array<string>} customComments - 可选的自定义评论内容数组，如果提供则使用此内容而不是配置中的评论
 */
function publishCommentInCurrentNote(noteTitle, noteAuthor, customComments) {
    // 改为同步操作，直接返回boolean结果
    try {
        utils.log("NOTE_COMMENT: Starting to publish comment in note: \"" + noteTitle + "\"");

        var commentConfig = configManager.getNoteCommentingConfig();
        utils.log("NOTE_COMMENT: Comment config: " + JSON.stringify(commentConfig));

        if (!commentConfig.enableCommenting) {
            utils.log("NOTE_COMMENT: Comment feature not enabled, skipping");
            return false;
        }

        // 去重检查：虽然main.js中已经检查过，但这里再次确认以确保数据一致性
        var dedupManager = null;
        if (commentConfig.enableDeduplication) {
            dedupManager = new CommentDeduplicationManager();
            if (dedupManager.isNoteCommented(noteTitle, noteAuthor || "Unknown Author")) {
                utils.log("NOTE_COMMENT: Note \"" + noteTitle + "\" already commented, skipping");
                return false;
            }
            utils.log("NOTE_COMMENT: Note \"" + noteTitle + "\" not commented before, continuing");
        }

        // 准备评论内容（AI生成或预设评论）
        var commentsToUse = [];
        var doubaoWebViewInstance = null; // 用于存储WebView实例，评论发布后关闭

        // AI评论生成逻辑
        if (commentConfig.enableLlmComments) {
            utils.log("NOTE_COMMENT: AI评论已启用，开始生成...");

            try {
                // 获取LLM服务模块
                const llmService = require('./llm_service.js');
                const shareLinkModule = require('./xhs_share_link.js');

                var llmInputContent = null;
                var inputType = "";

                // 根据用户选择的分析方式获取输入内容
                if (commentConfig.llmUseShareLink) {
                    // 使用分享链接方式
                    utils.log("NOTE_COMMENT: Using share link method for LLM analysis");

                    if (shareLinkModule && typeof shareLinkModule.copyShareLinkByNoteType === 'function') {
                        try {
                            var shareLink = shareLinkModule.copyShareLinkByNoteType(
                                require('./xhs_note_types.js').detectNoteTypeInDetailPage()
                            );

                            if (shareLink && shareLink.trim() !== "") {
                                llmInputContent = shareLink.trim();
                                inputType = "分享链接";
                                utils.log("NOTE_COMMENT: Share link copied successfully: " + shareLink.substring(0, 50) + "...");
                            } else {
                                utils.log("NOTE_COMMENT: Failed to copy share link");
                            }
                        } catch (e) {
                            utils.log("NOTE_COMMENT: Exception while copying share link: " + e.toString());
                        }
                    } else {
                        utils.log("NOTE_COMMENT: Share link module not available");
                    }
                } else if (commentConfig.llmUseContentExtraction) {
                    // 使用内容提取方式
                    utils.log("NOTE_COMMENT: Using content extraction method for LLM analysis");

                    // 提取笔记内容 - 使用简化的内容提取逻辑
                    var extractedText = extractNoteContentSimple();

                    if (extractedText && extractedText.trim() !== "") {
                        llmInputContent = extractedText.trim();
                        inputType = "笔记内容";
                        utils.log("NOTE_COMMENT: Content extracted successfully, length: " + llmInputContent.length);
                    } else {
                        utils.log("NOTE_COMMENT: No text extracted from note");
                    }
                } else {
                    utils.log("NOTE_COMMENT: No LLM analysis method selected");
                }

                // 如果有输入内容，调用LLM生成评论
                if (llmInputContent) {
                    var actualUserPrompt = null;
                    var selectedPromptName = commentConfig.selectedLlmPromptTemplateName;
                    var promptTemplates = commentConfig.llmPromptTemplates;

                    if (selectedPromptName && Array.isArray(promptTemplates) && promptTemplates.length > 0) {
                        var selectedTemplate = promptTemplates.find(function (t) { return t.name === selectedPromptName; });
                        if (selectedTemplate && selectedTemplate.content && selectedTemplate.content.trim() !== "") {
                            actualUserPrompt = selectedTemplate.content;
                            utils.log("NOTE_COMMENT: Using LLM prompt template: '" + selectedPromptName + "'");
                        } else {
                            utils.log("NOTE_COMMENT: LLM prompt template '" + selectedPromptName + "' not found or content is empty.");
                        }
                    } else {
                        utils.log("NOTE_COMMENT: No selected LLM prompt template name or templates array is invalid/empty.");
                    }

                    if (actualUserPrompt) {
                        try {
                            utils.log("NOTE_COMMENT: 调用AI分析" + inputType + "...");

                            // 根据配置选择API URL和相关参数
                            var apiUrl, modelName;
                            if (commentConfig.useDoubaoProxy) {
                                // 豆包AI模式：使用特殊标识，让LLM服务知道使用WebView
                                apiUrl = "DOUBAO_WEBVIEW_MODE";
                                modelName = "doubao";
                            } else {
                                apiUrl = commentConfig.llmApiUrl;
                                modelName = commentConfig.llmModelName;
                            }

                            var llmResult = llmService.generateCommentWithLLM(
                                llmInputContent,
                                apiUrl,
                                modelName,
                                actualUserPrompt,
                                commentConfig.llmTemperature,
                                commentConfig.llmMaxTokens,
                                commentConfig.useDoubaoProxy,
                                commentConfig.doubaoPhoneNumber,
                                commentConfig.doubaoManualCode || false
                            );

                            if (llmResult.success && llmResult.comment && llmResult.comment.trim() !== "") {
                                commentsToUse = [llmResult.comment.trim()];
                                utils.log("NOTE_COMMENT: LLM generated comment from " + inputType + ": " + llmResult.comment.trim().substring(0, 50));

                                // 保存WebView实例，用于评论发布后关闭
                                doubaoWebViewInstance = llmResult.webview;
                            } else {
                                utils.log("NOTE_COMMENT: LLM failed or no comment: " + (llmResult.error || "Empty LLM comment"));
                                // 回退到预设评论
                                commentsToUse = commentConfig.customComments;
                            }
                        } catch (e) {
                            utils.log("NOTE_COMMENT: LLM service call exception: " + e.toString());
                            // 回退到预设评论
                            commentsToUse = commentConfig.customComments;
                        }
                    } else {
                        utils.log("NOTE_COMMENT: actualUserPrompt is null. Using preset comments.");
                        commentsToUse = commentConfig.customComments;
                    }
                } else {
                    utils.log("NOTE_COMMENT: No valid input content for LLM analysis. Using preset comments.");
                    commentsToUse = commentConfig.customComments;
                }
            } catch (aiError) {
                utils.log("NOTE_COMMENT: AI comment generation error: " + aiError.toString());
                commentsToUse = commentConfig.customComments;
            }
        } else {
            // 使用传入的自定义评论或配置中的评论
            commentsToUse = customComments && customComments.length > 0 ? customComments : commentConfig.customComments;
        }

        if (customComments && customComments.length > 0) {
            utils.log("NOTE_COMMENT: Using custom provided comments: " + customComments.length + " available");
            utils.log("NOTE_COMMENT: Custom comments: " + JSON.stringify(customComments));
        } else if (commentsToUse && Array.isArray(commentsToUse) && commentsToUse.length > 0) {
            utils.log("NOTE_COMMENT: Using AI generated or config comments: " + commentsToUse.length + " available");
        } else {
            utils.log("NOTE_COMMENT: Using config comments: " + (commentConfig.customComments ? commentConfig.customComments.split('\n').length : 0) + " available");
        }

        // 对于非AI评论（预设评论），使用原有逻辑
        var commentManager = new CommentManager(commentsToUse, commentConfig.commentMode);

        if (commentManager.getCommentCount() === 0) {
            utils.log("NOTE_COMMENT: No available comment content, skipping");
            return false;
        }

        utils.log("NOTE_COMMENT: Using " + (customComments && customComments.length > 0 ? "custom provided" : "config") + " comments: " + commentManager.getCommentCount() + " available");

        var noteType = noteTypes.detectNoteTypeInDetailPage();
        utils.log("NOTE_COMMENT: Detected note type: " + noteTypes.getNoteTypeDescription(noteType));

        var commentText = commentManager.getNextComment();
        if (!commentText) {
            utils.log("NOTE_COMMENT: Unable to get comment content");
            return false;
        }

        // 执行安全控制的评论前延迟
        try {
            const safetyControl = require('./xhs_safety_control.js');
            if (safetyControl && typeof safetyControl.executePreCommentSafety === 'function') {
                utils.log("NOTE_COMMENT: 执行评论前安全延迟...");
                safetyControl.executePreCommentSafety(commentConfig);
            }
        } catch (safetyError) {
            utils.log("NOTE_COMMENT: 安全控制模块调用失败: " + safetyError.toString());
            // 继续执行，不因安全控制失败而中断评论
        }

        var publishSuccess = publishCommentByNoteType(noteType, commentText);

        if (publishSuccess) {
            utils.log("NOTE_COMMENT: Successfully published comment in note \"" + noteTitle + "\": \"" + commentText + "\"");

            if (commentConfig.enableDeduplication && dedupManager) {
                dedupManager.markNoteAsCommented(noteTitle, noteAuthor || "Unknown Author");
                var totalCommented = dedupManager.getCommentedNotesCount();
                utils.log("NOTE_COMMENT: Marked note as commented, total commented: " + totalCommented + " notes");
            }

            // 评论发布成功后，关闭WebView
            if (doubaoWebViewInstance && typeof doubaoWebViewInstance.cleanup === 'function') {
                try {
                    utils.log("NOTE_COMMENT: 评论发布成功，关闭豆包WebView");
                    doubaoWebViewInstance.cleanup();
                    utils.log("NOTE_COMMENT: 豆包WebView已关闭");
                } catch (webviewCleanupError) {
                    utils.log("NOTE_COMMENT: WebView关闭异常: " + webviewCleanupError, "warn");
                }
            }
        } else {
            utils.log("NOTE_COMMENT: Failed to publish comment in note \"" + noteTitle + "\": \"" + commentText + "\"");

            // 评论发布失败时也要关闭WebView
            if (doubaoWebViewInstance && typeof doubaoWebViewInstance.cleanup === 'function') {
                try {
                    utils.log("NOTE_COMMENT: 评论发布失败，关闭豆包WebView");
                    doubaoWebViewInstance.cleanup();
                    utils.log("NOTE_COMMENT: 豆包WebView已关闭");
                } catch (webviewCleanupError) {
                    utils.log("NOTE_COMMENT: WebView关闭异常: " + webviewCleanupError, "warn");
                }
            }
        }

        return publishSuccess;

    } catch (error) {
        utils.log("NOTE_COMMENT: Exception during comment publishing process: " + error);

        // 异常情况下也要关闭WebView
        if (doubaoWebViewInstance && typeof doubaoWebViewInstance.cleanup === 'function') {
            try {
                utils.log("NOTE_COMMENT: 异常情况下关闭豆包WebView");
                doubaoWebViewInstance.cleanup();
                utils.log("NOTE_COMMENT: 豆包WebView已关闭");
            } catch (webviewCleanupError) {
                utils.log("NOTE_COMMENT: WebView关闭异常: " + webviewCleanupError, "warn");
            }
        }

        return false;
    }
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        CommentManager,
        CommentDeduplicationManager,
        publishImageTextNoteComment,
        publishVideoNoteComment,
        publishCommentByNoteType,
        publishCommentInCurrentNote,
        // 导出清理函数供外部调用
        clearAllCommentedNotes: function () {
            var dedupManager = new CommentDeduplicationManager();
            return dedupManager.clearAllCommentedNotes();
        }
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.CommentManager = CommentManager;
    this.CommentDeduplicationManager = CommentDeduplicationManager;
    this.publishImageTextNoteComment = publishImageTextNoteComment;
    this.publishVideoNoteComment = publishVideoNoteComment;
    this.publishCommentByNoteType = publishCommentByNoteType;
    this.publishCommentInCurrentNote = publishCommentInCurrentNote;
    // 导出清理函数供外部调用
    this.clearAllCommentedNotes = function () {
        var dedupManager = new CommentDeduplicationManager();
        return dedupManager.clearAllCommentedNotes();
    };
}

utils.log("NOTE_COMMENT: XHS Note Commenting Module loaded successfully.");
