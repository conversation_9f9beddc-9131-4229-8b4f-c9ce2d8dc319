六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_safety_control.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: SAFETY: 安全控制初始化完成 - 今日评论次数: 0
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: xhs_safety_control.js loaded and initialized successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_account_manager.js...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: ACCOUNT: 账号管理器初始化完成 - 账号数量: 0, 当前索引: 0
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: xhs_account_manager.js loaded and initialized successfully.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:16 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 11, 2025 7:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: task_comment_notes checkbox changed, new state: true
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: Doubao AI enabled
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: 配置已加载到UI。
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:17 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 11, 2025 7:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:19 下午 GMT+08:00: MAIN_V3: 已提示用户关闭小红书app
六月 11, 2025 7:50:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require config.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: config.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require ui.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: ui.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_actions.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_safety_control.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: SAFETY: 安全控制初始化完成 - 今日评论次数: 0
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: xhs_safety_control.js loaded and initialized successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_account_manager.js...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: ACCOUNT: 账号管理器初始化完成 - 账号数量: 0, 当前索引: 0
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: xhs_account_manager.js loaded and initialized successfully.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:34 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 11, 2025 7:50:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: task_comment_notes checkbox changed, new state: true
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: Doubao AI enabled
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: 配置已加载到UI。
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:35 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 11, 2025 7:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:50:37 下午 GMT+08:00: MAIN_V3: 已提示用户关闭小红书app
六月 11, 2025 7:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: 开始任务按钮被点击
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: UI: 提醒用户确保小红书已关闭
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: 开始执行任务，选择的任务类型：笔记截流 
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: 开始搜索笔记，参数：{"keyword":"旅游","sortBy":3,"publishTime":2,"locationDistance":1,"targetRegion":0}
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: MAIN_V3: Received search request: {"keyword":"旅游","sortBy":3,"publishTime":2,"locationDistance":1,"targetRegion":0}
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: PPACTIONS: 开始笔记搜索: 关键字="旅游", 排序=3, 时间=2, 位置=1
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS: 尝试确保App '小红书' 打开并切换到前台，强制重启: false
六月 11, 2025 7:51:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:01 下午 GMT+08:00: UTILS: 启动App '小红书' (com.xingin.xhs)...
六月 11, 2025 7:51:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:01 下午 GMT+08:00: UTILS: App '小红书' 启动命令执行成功。等待应用响应...
六月 11, 2025 7:51:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:04 下午 GMT+08:00: UTILS: App '小红书' 已成功切换到前台。
六月 11, 2025 7:51:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:04 下午 GMT+08:00: PPACTIONS: 小红书App已准备好。
六月 11, 2025 7:51:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:06 下午 GMT+08:00: PPACTIONS: 找到首页搜索图标/按钮，点击进入搜索页。
六月 11, 2025 7:51:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:09 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 1)...
六月 11, 2025 7:51:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:11 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 2)...
六月 11, 2025 7:51:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:13 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 3)...
六月 11, 2025 7:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:13 下午 GMT+08:00: PPACTIONS: 找到搜索框 (方法 3)。Bounds: Rect(228, 114 - 786, 210)
六月 11, 2025 7:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:13 下午 GMT+08:00: PPACTIONS: 准备向搜索框输入文本...
六月 11, 2025 7:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:13 下午 GMT+08:00: PPACTIONS: 已调用 setText 输入关键字: 旅游
六月 11, 2025 7:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:14 下午 GMT+08:00: PPACTIONS: 准备触发搜索。
六月 11, 2025 7:51:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:14 下午 GMT+08:00: PPACTIONS: 步骤A - 尝试确保搜索框或其父控件有焦点...
六月 11, 2025 7:51:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:14 下午 GMT+08:00: PPACTIONS: 点击焦点目标: Rect(228, 114 - 786, 210) 以确保焦点。
六月 11, 2025 7:51:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:15 下午 GMT+08:00: PPACTIONS: 步骤B - 尝试查找并点击页面上的搜索按钮...
六月 11, 2025 7:51:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:15 下午 GMT+08:00: PPACTIONS: 尝试查找搜索确认按钮 (策略: Exact ID 'com.xingin.xhs:id/gcs', Button, Clickable)...
六月 11, 2025 7:51:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:16 下午 GMT+08:00: PPACTIONS: 找到潜在搜索按钮。详情: Text: "搜索", Desc: "null", ID: com.xingin.xhs:id/gcs, Class: android.widget.Button, Bounds: Rect(942, 96 - 1032, 228), Clickable: true, Visible: true
六月 11, 2025 7:51:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:16 下午 GMT+08:00: PPACTIONS: 按钮可见且可点击，尝试执行 click()...
六月 11, 2025 7:51:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:16 下午 GMT+08:00: PPACTIONS: searchConfirmButton.click() 执行成功 (返回true)。等待页面跳转...
六月 11, 2025 7:51:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 搜索已触发，准备应用筛选条件...
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 需要应用排序、发布时间或位置筛选，尝试打开主筛选面板。
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDJ16_Button)
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 找到主筛选按钮候选 (策略: FilterButtonByIDJ16_Button)。详情: Text: "", Desc: "null", ID: com.xingin.xhs:id/j16, Class: android.widget.Button, Bounds: Rect(822, 228 - 1068, 360), Clickable: true, Visible: true
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 主筛选按钮候选可用，将使用此按钮 (策略: FilterButtonByIDJ16_Button)。
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:21 下午 GMT+08:00: PPACTIONS: 点击主筛选按钮: null
六月 11, 2025 7:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:23 下午 GMT+08:00: PPACTIONS: Validating if filter panel is open after clicking main filter button...
六月 11, 2025 7:51:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: Panel validation details (using new container ID and text selectors):
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00:   - Panel container (bg9): not found
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00:   - Sort trigger ('综合'): found, visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00:   - Publish time option ('发布时间'): found, visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00:   - Location option ('搜索范围'): found, visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: Filter panel confirmed open: At least one characteristic element is visible.
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: 在筛选面板内开始应用排序依据: 最多评论
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: 尝试直接查找并点击目标排序选项 "最多评论"
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: 尝试定位排序选项文本元素: text("最多评论")
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: 已定位排序选项 "最多评论"，调用 tryClickInPanel。
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "排序选项 "最多评论"".
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "最多评论", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "排序选项 "最多评论"" 文本元素自身.
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "排序选项 "最多评论"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "排序选项 "最多评论"".
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "排序选项 "最多评论"" 不可见 (true) 或不可点击 (false).
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "排序选项 "最多评论"".
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(807, 502 - 1030, 622), Clickable: true, Visible: true
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "排序选项 "最多评论"" 可见且可点击. 尝试 click().
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "排序选项 "最多评论"" 的父控件 (层级 2).
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 的点击操作已标记为成功.
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:25 下午 GMT+08:00: PPACTIONS: 成功处理点击排序选项 "最多评论".
六月 11, 2025 7:51:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:27 下午 GMT+08:00: PPACTIONS: Delaying after sort option processing (1s).
六月 11, 2025 7:51:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: 尝试应用发布时间: "一周内"
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: 尝试定位发布时间选项文本元素: text("一周内")
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: 已定位发布时间选项 "一周内"，调用 tryClickInPanel。
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "发布时间选项 "一周内"".
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "一周内", Desc: "null", Bounds: Rect(593, 1210 - 737, 1318), Clickable: false, Visible: true
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "发布时间选项 "一周内"" 文本元素自身.
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "发布时间选项 "一周内"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "发布时间选项 "一周内"".
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(593, 1210 - 737, 1318), Clickable: false, Visible: true
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "发布时间选项 "一周内"" 不可见 (true) 或不可点击 (false).
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "发布时间选项 "一周内"".
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(554, 1204 - 777, 1324), Clickable: true, Visible: true
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "发布时间选项 "一周内"" 可见且可点击. 尝试 click().
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "发布时间选项 "一周内"" 的父控件 (层级 2).
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 的点击操作已标记为成功.
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:29 下午 GMT+08:00: PPACTIONS: 成功处理点击发布时间选项 "一周内".
六月 11, 2025 7:51:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:31 下午 GMT+08:00: PPACTIONS: Delaying after publish time option processing (1s).
六月 11, 2025 7:51:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 尝试应用位置距离: "同城"
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 尝试定位位置距离选项文本元素: text("同城")
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 已定位位置距离选项 "同城"，调用 tryClickInPanel。
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "同城", Desc: "null", Bounds: Rect(361, 1756 - 463, 1717), Clickable: false, Visible: false
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "位置距离选项 "同城"" 文本元素自身.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 自身不可见 (visibleToUser: false). 跳过直接点击自身.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "位置距离选项 "同城"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(361, 1756 - 463, 1717), Clickable: false, Visible: false
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "位置距离选项 "同城"" 不可见 (false) 或不可点击 (false).
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1750 - 524, 1717), Clickable: true, Visible: false
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "位置距离选项 "同城"" 不可见 (false) 或不可点击 (true).
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 3) for "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 3) 属性: ID: com.xingin.xhs:id/cwg, Class: androidx.recyclerview.widget.RecyclerView, Text: "", Desc: "null", Bounds: Rect(48, 1750 - 1032, 1717), Clickable: false, Visible: false
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 3) for "位置距离选项 "同城"" 不可见 (false) 或不可点击 (false).
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 4) for "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 4) 属性: ID: null, Class: android.widget.LinearLayout, Text: "", Desc: "null", Bounds: Rect(48, 1657 - 1032, 1717), Clickable: false, Visible: true
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 4) for "位置距离选项 "同城"" 不可见 (true) 或不可点击 (false).
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 未能通过查找父控件点击 "位置距离选项 "同城"".
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 3: 前两步均失败 for "位置距离选项 "同城"". 最后尝试直接点击原始文本元素.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: WARN: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 原始文本元素在“最后尝试”时不可见. 无法点击.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: WARN: PPACTIONS: [tryClickInPanel] 所有点击尝试对 "位置距离选项 "同城"" 均失败.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: WARN: PPACTIONS: 未能成功点击位置距离选项 "同城" 通过任何方法.
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 已定位收起按钮 (ID: bad)，尝试点击。
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:33 下午 GMT+08:00: PPACTIONS: 成功点击收起按钮。
六月 11, 2025 7:51:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:35 下午 GMT+08:00: PPACTIONS: 笔记搜索和筛选操作流程结束。
六月 11, 2025 7:51:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:35 下午 GMT+08:00: MAIN_V3: 搜索成功。 搜索和筛选流程已执行
六月 11, 2025 7:51:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:38 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 11, 2025 7:51:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:38 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 11, 2025 7:51:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: 搜索完成，开始笔记截流(评论笔记)
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: startNoteCommenting task initiated by UI.
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread started.
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 找到 4 个笔记容器
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 处理容器 1/4
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大..."
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 作者: "厦门地陪阿信"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行", 作者: "厦门地陪阿信", 评论数: 0, signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 处理容器 2/4
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩..."
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 作者: "远妈妈"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩", 作者: "远妈妈", 评论数: 0, signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 处理容器 3/4
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤..."
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 作者: "陈北东。"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 处理容器 4/4
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷..."
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 作者: "糖果"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 提取完成，找到 4 个有效笔记
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行", 作者: "厦门地陪阿信", 评论数: 0, signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩", 作者: "远妈妈", 评论数: 0, signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 4 个笔记，开始处理
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/4
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "厦门地陪阿信"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:39 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行
六月 11, 2025 7:51:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:44 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 11, 2025 7:51:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: PPACTIONS: 未找到 gn_ 元素且找到 c9y 容器 - 确认为视频笔记详情页
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: false, comment: false, gn_: false) = false
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: true
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:46 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:51:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: video
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用视频笔记处理模块
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: USER_PROFILE: 小红书用户信息采集模块加载完毕
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_COMMENTS: 小红书视频笔记评论采集模块 (xhs_video_comments.js) 加载完毕。
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 开始处理视频笔记: 厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_comment_notes: true
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - enableLlmComments: true
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_collect_users: false
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_like_users: false
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: VIDEO_PROCESS: 开始评论发布...
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行"
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:48 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:51:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:49 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:51:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:49 下午 GMT+08:00: SHARE_LINK: 开始复制视频笔记分享链接
六月 11, 2025 7:51:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: SHARE_LINK: 未找到视频笔记容器 (wm)
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: NOTE_COMMENT: Failed to copy share link
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: NOTE_COMMENT: No valid input content for LLM analysis. Using preset comments.
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: NOTE_COMMENT: Using config comments: 0 available
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: COMMENT_MANAGER: Initialized with 0 comments, mode: random
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: NOTE_COMMENT: No available comment content, skipping
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: VIDEO_PROCESS: ✗ 评论发布失败或跳过
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: VIDEO_PROCESS: 用户信息采集功能未启用 (task_collect_users: false)，跳过
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: VIDEO_PROCESS: 返回搜索结果页...
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:52 下午 GMT+08:00: PPACTIONS: 未传入笔记类型，进行实时检测...
六月 11, 2025 7:51:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:53 下午 GMT+08:00: PPACTIONS: 未检测到图文笔记标识，使用视频笔记返回逻辑。
六月 11, 2025 7:51:53 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:56 下午 GMT+08:00: PPACTIONS: 视频笔记返回按钮未找到，尝试全局返回键。
六月 11, 2025 7:51:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:58 下午 GMT+08:00: VIDEO_PROCESS: 视频笔记处理完成，成功: false
六月 11, 2025 7:51:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:58 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学...
六月 11, 2025 7:51:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:58 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 11, 2025 7:51:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/4
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩"
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "远妈妈"
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:51:59 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩
六月 11, 2025 7:51:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:03 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: video
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用视频笔记处理模块
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 开始处理视频笔记: 今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_comment_notes: true
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - enableLlmComments: true
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_collect_users: false
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_like_users: false
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: VIDEO_PROCESS: 开始评论发布...
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩"
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:04 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:05 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:05 下午 GMT+08:00: SHARE_LINK: 开始复制视频笔记分享链接
六月 11, 2025 7:52:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: SHARE_LINK: 未找到视频笔记容器 (wm)
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: NOTE_COMMENT: Failed to copy share link
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: NOTE_COMMENT: No valid input content for LLM analysis. Using preset comments.
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: NOTE_COMMENT: Using config comments: 0 available
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: COMMENT_MANAGER: Initialized with 0 comments, mode: random
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: NOTE_COMMENT: No available comment content, skipping
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: VIDEO_PROCESS: ✗ 评论发布失败或跳过
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: VIDEO_PROCESS: 用户信息采集功能未启用 (task_collect_users: false)，跳过
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: VIDEO_PROCESS: 返回搜索结果页...
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: PPACTIONS: 未传入笔记类型，进行实时检测...
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: PPACTIONS: 检测到图文笔记，使用图文笔记返回逻辑。
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:08 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮点击成功
六月 11, 2025 7:52:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:10 下午 GMT+08:00: VIDEO_PROCESS: 视频笔记处理完成，成功: false
六月 11, 2025 7:52:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:10 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游...
六月 11, 2025 7:52:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:10 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 11, 2025 7:52:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/4
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读"
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "陈北东。"
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读
六月 11, 2025 7:52:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:15 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: video
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用视频笔记处理模块
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 开始处理视频笔记: 旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_comment_notes: true
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - enableLlmComments: true
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_collect_users: false
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_like_users: false
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: VIDEO_PROCESS: 开始评论发布...
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读"
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:16 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:17 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:17 下午 GMT+08:00: SHARE_LINK: 开始复制视频笔记分享链接
六月 11, 2025 7:52:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: SHARE_LINK: 未找到视频笔记容器 (wm)
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: NOTE_COMMENT: Failed to copy share link
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: NOTE_COMMENT: No valid input content for LLM analysis. Using preset comments.
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: NOTE_COMMENT: Using config comments: 0 available
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: COMMENT_MANAGER: Initialized with 0 comments, mode: random
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: NOTE_COMMENT: No available comment content, skipping
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: VIDEO_PROCESS: ✗ 评论发布失败或跳过
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: VIDEO_PROCESS: 用户信息采集功能未启用 (task_collect_users: false)，跳过
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: VIDEO_PROCESS: 返回搜索结果页...
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: PPACTIONS: 未传入笔记类型，进行实时检测...
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:20 下午 GMT+08:00: PPACTIONS: 检测到图文笔记，使用图文笔记返回逻辑。
六月 11, 2025 7:52:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:21 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮点击成功
六月 11, 2025 7:52:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:22 下午 GMT+08:00: VIDEO_PROCESS: 视频笔记处理完成，成功: false
六月 11, 2025 7:52:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一...
六月 11, 2025 7:52:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 11, 2025 7:52:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/4
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义"
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "糖果"
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:23 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义
六月 11, 2025 7:52:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:27 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: video
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用视频笔记处理模块
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 开始处理视频笔记: 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_comment_notes: true
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - enableLlmComments: true
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_collect_users: false
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 配置信息 - task_like_users: false
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: VIDEO_PROCESS: 开始评论发布...
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义"
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:29 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 11, 2025 7:52:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:30 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 11, 2025 7:52:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:30 下午 GMT+08:00: SHARE_LINK: 开始复制视频笔记分享链接
六月 11, 2025 7:52:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: SHARE_LINK: 未找到视频笔记容器 (wm)
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: NOTE_COMMENT: Failed to copy share link
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: NOTE_COMMENT: No valid input content for LLM analysis. Using preset comments.
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: NOTE_COMMENT: Using config comments: 0 available
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: COMMENT_MANAGER: Initialized with 0 comments, mode: random
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: NOTE_COMMENT: No available comment content, skipping
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: VIDEO_PROCESS: ✗ 评论发布失败或跳过
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: VIDEO_PROCESS: 用户信息采集功能未启用 (task_collect_users: false)，跳过
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: VIDEO_PROCESS: 返回搜索结果页...
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: PPACTIONS: 未传入笔记类型，进行实时检测...
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: PPACTIONS: 检测到图文笔记，使用图文笔记返回逻辑。
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:33 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮点击成功
六月 11, 2025 7:52:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:34 下午 GMT+08:00: VIDEO_PROCESS: 视频笔记处理完成，成功: false
六月 11, 2025 7:52:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:34 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体...
六月 11, 2025 7:52:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:34 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 11, 2025 7:52:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:35 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 11, 2025 7:52:36 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:36 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 11, 2025 7:52:36 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:39 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 11, 2025 7:52:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 96 - 1068, 228)
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 找到 6 个笔记容器
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 处理容器 1/6
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大..."
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 作者: "厦门地陪阿信"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行", 作者: "厦门地陪阿信", 评论数: 0, signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 处理容器 2/6
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩..."
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 作者: "远妈妈"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩", 作者: "远妈妈", 评论数: 0, signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 处理容器 3/6
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤..."
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 作者: "陈北东。"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 处理容器 4/6
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷..."
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 作者: "糖果"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:42 下午 GMT+08:00: PPACTIONS: 处理容器 5/6
六月 11, 2025 7:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原..."
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 作者: "艾米丽的旅行日记"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天美哭啦～ 蓝天白云，橘子日落，风吹草低见牛羊 仿佛走进宫崎骏的", 作者: "艾米丽的旅行日记", 评论数: 0, signature: "艾米丽的旅行日记::Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 处理容器 6/6
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿..."
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 作者: "超奇说旅游"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#那拉提 ﻿ ﻿#新疆小团 ﻿ ﻿#夏塔", 作者: "超奇说旅游", 评论数: 0, signature: "超奇说旅游::纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 提取完成，找到 6 个有效笔记
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行", 作者: "厦门地陪阿信", 评论数: 0, signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩", 作者: "远妈妈", 评论数: 0, signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天美哭啦～ 蓝天白云，橘子日落，风吹草低见牛羊 仿佛走进宫崎骏的", 作者: "艾米丽的旅行日记", 评论数: 0, signature: "艾米丽的旅行日记::Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 数组[5] - 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#那拉提 ﻿ ﻿#新疆小团 ﻿ ﻿#夏塔", 作者: "超奇说旅游", 评论数: 0, signature: "超奇说旅游::纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 4 个笔记，开始处理
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/4
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "厦门地陪阿信"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学...
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/4
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "远妈妈"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游...
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/4
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "陈北东。"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一...
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/4
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "糖果"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体...
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕所有笔记都已处理过，准备滚动到下一页
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:43 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 11, 2025 7:52:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:46 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 11, 2025 7:52:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 96 - 1068, 228)
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:49 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 11, 2025 7:52:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 找到 6 个笔记容器
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 1/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "陈北东。"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 2/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "糖果"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 3/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "艾米丽的旅行日记"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天美哭啦～ 蓝天白云，橘子日落，风吹草低见牛羊 仿佛走进宫崎骏的", 作者: "艾米丽的旅行日记", 评论数: 0, signature: "艾米丽的旅行日记::Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 4/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "超奇说旅游"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#那拉提 ﻿ ﻿#新疆小团 ﻿ ﻿#夏塔", 作者: "超奇说旅游", 评论数: 0, signature: "超奇说旅游::纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 5/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "人麻了，来埃及一天被坑800遍 #埃及旅游  #埃及  #金..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "油头四六分"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "人麻了，来埃及一天被坑800遍 #埃及旅游  #埃及  #金字塔  #出国  #全世界旅游  #失落的文明", 作者: "油头四六分", 评论数: 0, signature: "油头四六分::人麻了，来埃及一天被坑800遍 #埃及旅游  #埃及  #金字塔  #出国  #全世界旅游  #失落"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 处理容器 6/6
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 标题: "女生工资不高但朋友圈经常旅游 #女孩子旅游  #女孩子经济独..."
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 作者: "钱朵朵"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "女生工资不高但朋友圈经常旅游 #女孩子旅游  #女孩子经济独立  #先旅游还是先攒钱  #女性经济自由  #攒钱旅游想去", 作者: "钱朵朵", 评论数: 0, signature: "钱朵朵::女生工资不高但朋友圈经常旅游 #女孩子旅游  #女孩子经济独立  #先旅游还是先攒钱  #女性经济自"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 提取完成，找到 6 个有效笔记
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读", 作者: "陈北东。", 评论数: 0, signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义", 作者: "糖果", 评论数: 0, signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天美哭啦～ 蓝天白云，橘子日落，风吹草低见牛羊 仿佛走进宫崎骏的", 作者: "艾米丽的旅行日记", 评论数: 0, signature: "艾米丽的旅行日记::Live图的意义，呼伦贝尔第一天就美哭了 江浙沪牛马来大草原的第一天🐂🐴🐑 就被呼伦贝尔的夏天"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#那拉提 ﻿ ﻿#新疆小团 ﻿ ﻿#夏塔", 作者: "超奇说旅游", 评论数: 0, signature: "超奇说旅游::纯粹到极致的伊犁攻略 ﻿#新疆旅游攻略 ﻿ ﻿#新疆旅游 ﻿ ﻿#赛里木湖 ﻿ ﻿#唐布拉 ﻿ ﻿#"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "人麻了，来埃及一天被坑800遍 #埃及旅游  #埃及  #金字塔  #出国  #全世界旅游  #失落的文明", 作者: "油头四六分", 评论数: 0, signature: "油头四六分::人麻了，来埃及一天被坑800遍 #埃及旅游  #埃及  #金字塔  #出国  #全世界旅游  #失落"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 数组[5] - 标题: "女生工资不高但朋友圈经常旅游 #女孩子旅游  #女孩子经济独立  #先旅游还是先攒钱  #女性经济自由  #攒钱旅游想去", 作者: "钱朵朵", 评论数: 0, signature: "钱朵朵::女生工资不高但朋友圈经常旅游 #女孩子旅游  #女孩子经济独立  #先旅游还是先攒钱  #女性经济自"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 4 个笔记，开始处理
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/4
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦门地陪  #厦门导游  #厦门  #厦门旅游  #厦门旅行"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "厦门地陪阿信"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学游玩攻略  #厦"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 厦门地陪阿信::厦门大学错别字⁉️厦门地陪导游告诉你 #厦门大学  #厦门大学预约攻略  #厦门大学...
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/4
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  #推荐一个好地方  #假期去哪玩"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "远妈妈"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游的地方  "
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 远妈妈::今年暑假推荐去哪里亲子游？ 7-10天，10岁男孩，人少好玩，可以深度游的。#适合亲子旅游...
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/4
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老人家一起住，老公的大哥两夫妻一起在外打工，读"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "陈北东。"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一家三口还跟老"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 陈北东。::旅游不想带亲戚孩子一起 旅游不想带亲戚孩子 怎么操作比较不伤感情 家庭背景： 目前我们一...
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/4
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略  #生活美学  #人生的意义"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "糖果"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体验感又好"
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 糖果::你花费最低体验感又好的旅游城市是哪个[萌萌哒R]#旅游 #穷游攻略 #生活美学 你花费最低体...
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕所有笔记都已处理过，准备滚动到下一页
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:50 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 11, 2025 7:52:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 11, 2025 7:52:54 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 11, 2025 7:52:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
