六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require config.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: config.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require ui.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: ui.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_actions.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_safety_control.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: SAFETY: 安全控制初始化完成 - 今日评论次数: 0
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: xhs_safety_control.js loaded and initialized successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_account_manager.js...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: ACCOUNT: 账号管理器初始化完成 - 账号数量: 0, 当前索引: 0
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: xhs_account_manager.js loaded and initialized successfully.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:28 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 12, 2025 12:48:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: task_comment_notes checkbox changed, new state: true
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: Doubao AI enabled
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: doubaoAutoSmsCode状态已保存: false
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: 豆包手动输入验证码已选择，自动获取已取消
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: doubaoManualCode状态已保存: true
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:29 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 12, 2025 12:48:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: 配置已加载到UI。
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:30 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 12, 2025 12:48:30 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:32 下午 GMT+08:00: MAIN_V3: 已提示用户关闭小红书app
六月 12, 2025 12:48:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:38 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: === 开始批量更新元素ID ===
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 计划更新 7 个文件
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 计划替换 33 个元素ID
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_actions.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_actions.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_comment_actions.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_comment_actions.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_simple_comments.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_simple_comments.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_video_comments.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_video_comments.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_note_commenting.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_note_commenting.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_share_link.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_share_link.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始更新文件: xhs_note_types.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 文件无需更新: xhs_note_types.js
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: === 批量更新完成 ===
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 成功更新: 7/7 个文件
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ 更新报告已保存: id_update_report_2025-06-12T04-48-40-412Z.txt
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: 开始验证更新结果...
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_actions.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_comment_actions.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_simple_comments.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_video_comments.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_note_commenting.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_share_link.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:40 下午 GMT+08:00: BATCH_UPDATE: ✅ xhs_note_types.js - 所有旧ID已更新
六月 12, 2025 12:48:40 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require config.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: config.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require ui.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: ui.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_actions.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_safety_control.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: SAFETY: 安全控制初始化完成 - 今日评论次数: 0
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: xhs_safety_control.js loaded and initialized successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_account_manager.js...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: ACCOUNT: 账号管理器初始化完成 - 账号数量: 0, 当前索引: 0
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: xhs_account_manager.js loaded and initialized successfully.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:44 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 12, 2025 12:48:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: task_comment_notes checkbox changed, new state: true
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: Doubao AI enabled
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: doubaoAutoSmsCode状态已保存: false
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: 豆包手动输入验证码已选择，自动获取已取消
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: doubaoManualCode状态已保存: true
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: 配置已加载到UI。
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:45 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 12, 2025 12:48:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:48:47 下午 GMT+08:00: MAIN_V3: 已提示用户关闭小红书app
六月 12, 2025 12:48:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: 开始任务按钮被点击
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: UI: 提醒用户确保小红书已关闭
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: 开始执行任务，选择的任务类型：笔记截流 
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: 开始搜索笔记，参数：{"keyword":"旅游","sortBy":3,"publishTime":2,"locationDistance":1,"targetRegion":0}
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: MAIN_V3: Received search request: {"keyword":"旅游","sortBy":3,"publishTime":2,"locationDistance":1,"targetRegion":0}
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: PPACTIONS: 开始笔记搜索: 关键字="旅游", 排序=3, 时间=2, 位置=1
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS: 尝试确保App '小红书' 打开并切换到前台，强制重启: false
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS: 启动App '小红书' (com.xingin.xhs)...
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS: App '小红书' 启动命令执行成功。等待应用响应...
六月 12, 2025 12:50:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:05 下午 GMT+08:00: UTILS: App '小红书' 已成功切换到前台。
六月 12, 2025 12:50:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:05 下午 GMT+08:00: PPACTIONS: 小红书App已准备好。
六月 12, 2025 12:50:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:07 下午 GMT+08:00: PPACTIONS: 找到首页搜索图标/按钮，点击进入搜索页。
六月 12, 2025 12:50:07 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:10 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 1)...
六月 12, 2025 12:50:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:12 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 2)...
六月 12, 2025 12:50:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:15 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 3)...
六月 12, 2025 12:50:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:15 下午 GMT+08:00: PPACTIONS: 找到搜索框 (方法 3)。Bounds: Rect(228, 114 - 786, 210)
六月 12, 2025 12:50:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:15 下午 GMT+08:00: PPACTIONS: 准备向搜索框输入文本...
六月 12, 2025 12:50:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:15 下午 GMT+08:00: PPACTIONS: 已调用 setText 输入关键字: 旅游
六月 12, 2025 12:50:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:16 下午 GMT+08:00: PPACTIONS: 准备触发搜索。
六月 12, 2025 12:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:16 下午 GMT+08:00: PPACTIONS: 步骤A - 尝试确保搜索框或其父控件有焦点...
六月 12, 2025 12:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:16 下午 GMT+08:00: PPACTIONS: 点击焦点目标: Rect(228, 114 - 786, 210) 以确保焦点。
六月 12, 2025 12:50:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:17 下午 GMT+08:00: PPACTIONS: 步骤B - 尝试查找并点击页面上的搜索按钮...
六月 12, 2025 12:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:17 下午 GMT+08:00: PPACTIONS: 尝试查找搜索确认按钮 (策略: Exact ID 'com.xingin.xhs:id/gcs', Button, Clickable)...
六月 12, 2025 12:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:17 下午 GMT+08:00: PPACTIONS: 找到潜在搜索按钮。详情: Text: "搜索", Desc: "null", ID: com.xingin.xhs:id/gcs, Class: android.widget.Button, Bounds: Rect(942, 96 - 1032, 228), Clickable: true, Visible: true
六月 12, 2025 12:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:17 下午 GMT+08:00: PPACTIONS: 按钮可见且可点击，尝试执行 click()...
六月 12, 2025 12:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:17 下午 GMT+08:00: PPACTIONS: searchConfirmButton.click() 执行成功 (返回true)。等待页面跳转...
六月 12, 2025 12:50:17 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:22 下午 GMT+08:00: PPACTIONS: 搜索已触发，准备应用筛选条件...
六月 12, 2025 12:50:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:22 下午 GMT+08:00: PPACTIONS: 需要应用排序、发布时间或位置筛选，尝试打开主筛选面板。
六月 12, 2025 12:50:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:22 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDJ16_Button)
六月 12, 2025 12:50:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:23 下午 GMT+08:00: PPACTIONS: 找到主筛选按钮候选 (策略: FilterButtonByIDJ16_Button)。详情: Text: "", Desc: "null", ID: com.xingin.xhs:id/j16, Class: android.widget.Button, Bounds: Rect(822, 228 - 1068, 360), Clickable: true, Visible: true
六月 12, 2025 12:50:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:23 下午 GMT+08:00: PPACTIONS: 主筛选按钮候选可用，将使用此按钮 (策略: FilterButtonByIDJ16_Button)。
六月 12, 2025 12:50:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:23 下午 GMT+08:00: PPACTIONS: 点击主筛选按钮: null
六月 12, 2025 12:50:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:25 下午 GMT+08:00: PPACTIONS: Validating if filter panel is open after clicking main filter button...
六月 12, 2025 12:50:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: Panel validation details (using new container ID and text selectors):
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00:   - Panel container (bg9): not found
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00:   - Sort trigger ('综合'): found, visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00:   - Publish time option ('发布时间'): found, visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00:   - Location option ('搜索范围'): found, visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: Filter panel confirmed open: At least one characteristic element is visible.
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: 在筛选面板内开始应用排序依据: 最多评论
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: 尝试直接查找并点击目标排序选项 "最多评论"
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: 尝试定位排序选项文本元素: text("最多评论")
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: 已定位排序选项 "最多评论"，调用 tryClickInPanel。
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "排序选项 "最多评论"".
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "最多评论", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "排序选项 "最多评论"" 文本元素自身.
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "排序选项 "最多评论"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "排序选项 "最多评论"".
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "排序选项 "最多评论"" 不可见 (true) 或不可点击 (false).
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "排序选项 "最多评论"".
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(807, 502 - 1030, 622), Clickable: true, Visible: true
六月 12, 2025 12:50:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:26 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "排序选项 "最多评论"" 可见且可点击. 尝试 click().
六月 12, 2025 12:50:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:27 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "排序选项 "最多评论"" 的父控件 (层级 2).
六月 12, 2025 12:50:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:27 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 的点击操作已标记为成功.
六月 12, 2025 12:50:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:27 下午 GMT+08:00: PPACTIONS: 成功处理点击排序选项 "最多评论".
六月 12, 2025 12:50:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:29 下午 GMT+08:00: PPACTIONS: Delaying after sort option processing (1s).
六月 12, 2025 12:50:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: 尝试应用发布时间: "一周内"
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: 尝试定位发布时间选项文本元素: text("一周内")
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: 已定位发布时间选项 "一周内"，调用 tryClickInPanel。
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "发布时间选项 "一周内"".
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "一周内", Desc: "null", Bounds: Rect(593, 1210 - 737, 1318), Clickable: false, Visible: true
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "发布时间选项 "一周内"" 文本元素自身.
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "发布时间选项 "一周内"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "发布时间选项 "一周内"".
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(593, 1210 - 737, 1318), Clickable: false, Visible: true
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "发布时间选项 "一周内"" 不可见 (true) 或不可点击 (false).
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "发布时间选项 "一周内"".
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(554, 1204 - 777, 1324), Clickable: true, Visible: true
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "发布时间选项 "一周内"" 可见且可点击. 尝试 click().
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "发布时间选项 "一周内"" 的父控件 (层级 2).
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一周内"" 的点击操作已标记为成功.
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:31 下午 GMT+08:00: PPACTIONS: 成功处理点击发布时间选项 "一周内".
六月 12, 2025 12:50:31 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:33 下午 GMT+08:00: PPACTIONS: Delaying after publish time option processing (1s).
六月 12, 2025 12:50:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:35 下午 GMT+08:00: PPACTIONS: 尝试应用位置距离: "同城"
六月 12, 2025 12:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:35 下午 GMT+08:00: PPACTIONS: 尝试定位位置距离选项文本元素: text("同城")
六月 12, 2025 12:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:35 下午 GMT+08:00: WARN: PPACTIONS: 位置距离选项 "同城" 找到但不可见，尝试滚动筛选面板.
六月 12, 2025 12:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:35 下午 GMT+08:00: PPACTIONS: 尝试滚动筛选面板...
六月 12, 2025 12:50:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:36 下午 GMT+08:00: PPACTIONS: 滑动后找到位置距离选项，停止滑动
六月 12, 2025 12:50:36 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: 滚动后找到可见的位置距离选项: "同城"
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "位置距离选项 "同城"".
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 初始属性: ID: com.xingin.xhs:id/ety, Class: android.widget.TextView, Text: "同城", Desc: "null", Bounds: Rect(361, 1555 - 463, 1663), Clickable: false, Visible: true
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "位置距离选项 "同城"" 文本元素自身.
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "位置距离选项 "同城"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "位置距离选项 "同城"".
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(361, 1555 - 463, 1663), Clickable: false, Visible: true
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "位置距离选项 "同城"" 不可见 (true) 或不可点击 (false).
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "位置距离选项 "同城"".
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1549 - 524, 1669), Clickable: true, Visible: true
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "位置距离选项 "同城"" 可见且可点击. 尝试 click().
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "位置距离选项 "同城"" 的父控件 (层级 2).
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 的点击操作已标记为成功.
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:37 下午 GMT+08:00: PPACTIONS: 滚动后成功点击位置距离选项 "同城".
六月 12, 2025 12:50:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:39 下午 GMT+08:00: PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。
六月 12, 2025 12:50:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:39 下午 GMT+08:00: PPACTIONS: 已定位收起按钮 (ID: bad)，尝试点击。
六月 12, 2025 12:50:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:39 下午 GMT+08:00: PPACTIONS: 成功点击收起按钮。
六月 12, 2025 12:50:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:41 下午 GMT+08:00: PPACTIONS: 笔记搜索和筛选操作流程结束。
六月 12, 2025 12:50:41 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:41 下午 GMT+08:00: MAIN_V3: 搜索成功。 搜索和筛选流程已执行
六月 12, 2025 12:50:41 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: 搜索完成，开始笔记截流(评论笔记)
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:44 下午 GMT+08:00: MAIN_V3_COMMENTING: startNoteCommenting task initiated by UI.
六月 12, 2025 12:50:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread started.
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 找到 5 个笔记容器
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 处理容器 1/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 标题: "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全 ..."
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 作者: "岛民今天玩什么?（咨询版）"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚", 作者: "岛民今天玩什么?（咨询版）", 评论数: 0, signature: "岛民今天玩什么?（咨询版）::出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 处理容器 2/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 标题: "6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天..."
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 作者: "三亚王姐"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略  #三亚天气现状  #三亚天气穿衣", 作者: "三亚王姐", 评论数: 0, signature: "三亚王姐::6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略 "
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 处理容器 3/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 标题: "用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生 ..."
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 作者: "海南旅文"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉一夏海南度假  #三亚  #三亚立才农场", 作者: "海南旅文", 评论数: 0, signature: "海南旅文::用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 处理容器 4/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 标题: "海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #..."
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 作者: "海南郑秀晶-"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略  #跟着本地人  #一定要去的地方  #本地人做的攻", 作者: "海南郑秀晶-", 评论数: 0, signature: "海南郑秀晶-::海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 处理容器 5/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 标题: "在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万..."
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 作者: "海南郑秀晶-"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴还有美食、交通、避坑等全都是干货✌旅行小白也可以放心抄作业 ✍最", 作者: "海南郑秀晶-", 评论数: 0, signature: "海南郑秀晶-::在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 提取完成，找到 5 个有效笔记
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚", 作者: "岛民今天玩什么?（咨询版）", 评论数: 0, signature: "岛民今天玩什么?（咨询版）::出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略  #三亚天气现状  #三亚天气穿衣", 作者: "三亚王姐", 评论数: 0, signature: "三亚王姐::6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略 "
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉一夏海南度假  #三亚  #三亚立才农场", 作者: "海南旅文", 评论数: 0, signature: "海南旅文::用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略  #跟着本地人  #一定要去的地方  #本地人做的攻", 作者: "海南郑秀晶-", 评论数: 0, signature: "海南郑秀晶-::海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴还有美食、交通、避坑等全都是干货✌旅行小白也可以放心抄作业 ✍最", 作者: "海南郑秀晶-", 评论数: 0, signature: "海南郑秀晶-::在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 5 个笔记，开始处理
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/5
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "岛民今天玩什么?（咨询版）"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "岛民今天玩什么?（咨询版）::出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚"
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:45 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚
六月 12, 2025 12:50:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: image_text
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用图文笔记处理模块
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: SIMPLE_PROCESS: 开始处理图文笔记: 出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: SIMPLE_PROCESS: 开始评论发布...
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚"
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","doubaoAutoSmsCode":false,"doubaoManualCode":true,"llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: SHARE_LINK: 开始复制图文笔记分享链接
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:49 下午 GMT+08:00: SHARE_LINK: 点击图文笔记转发按钮
六月 12, 2025 12:50:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: 找到desc='复制链接'的元素
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: 分析desc元素的层级结构
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: desc元素信息 - class: android.widget.Button, clickable: false
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: desc元素有 1 个子元素
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: 子元素0 - class: android.view.ViewGroup, clickable: true
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: 找到可点击的ViewGroup子元素
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: 最终找到的可点击元素信息:
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: - ID: com.xingin.xhs:id/jb0
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: - Class: android.view.ViewGroup
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: - Desc: null
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: - Text: null
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:51 下午 GMT+08:00: SHARE_LINK: - Clickable: true
六月 12, 2025 12:50:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:52 下午 GMT+08:00: SHARE_LINK: - Bounds: {"bottom":2103,"left":501,"right":699,"top":1833}
六月 12, 2025 12:50:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:52 下午 GMT+08:00: SHARE_LINK: 已清空剪贴板
六月 12, 2025 12:50:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:52 下午 GMT+08:00: SHARE_LINK: 点击复制链接按钮
六月 12, 2025 12:50:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:52 下午 GMT+08:00: SHARE_LINK: 直接点击成功
六月 12, 2025 12:50:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:52 下午 GMT+08:00: SHARE_LINK: 等待复制完成...
六月 12, 2025 12:50:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: SHARE_LINK: 使用悬浮窗方式获取剪贴板内容
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: SHARE_LINK: 悬浮窗方式获取到剪贴板内容: 43 岛民今天玩什么?（咨询版）发布了一篇小红书笔记，快来看吧！ 😆 r1RP9Q87slVakf2 😆 http://xhslink.com/a/cB9Z9J42nJKeb，复制本条信息，打开【小红书】App查看精彩内容！
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: SHARE_LINK: 剪贴板内容: 43 岛民今天玩什么?（咨询版）发布了一篇小红书笔记，快来看吧！ 😆 r1RP9Q87slVakf2 😆 http://xhslink.com/a/cB9Z9J42nJKeb，复制本条信息，打开【小红书】App查看精彩内容！
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: NOTE_COMMENT: Share link copied successfully: 43 岛民今天玩什么?（咨询版）发布了一篇小红书笔记，快来看吧！ 😆 r1RP9Q87slVakf...
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: NOTE_COMMENT: Using LLM prompt template: '旅游'
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: NOTE_COMMENT: 调用AI分析分享链接...
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: LLM_SERVICE: 使用豆包AI模式
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: DOUBAO_WEBVIEW: 开始初始化豆包WebView...
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:54 下午 GMT+08:00: DOUBAO_WEBVIEW: 创建悬浮窗...
六月 12, 2025 12:50:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 设置悬浮窗属性...
六月 12, 2025 12:50:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 等待悬浮窗创建完成...
六月 12, 2025 12:50:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:58 下午 GMT+08:00: DOUBAO_WEBVIEW: 获取WebView控件...
六月 12, 2025 12:50:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:58 下午 GMT+08:00: DOUBAO_WEBVIEW: 配置WebView...
六月 12, 2025 12:50:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:58 下午 GMT+08:00: DOUBAO_WEBVIEW: 加载豆包移动版网站...
六月 12, 2025 12:50:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:50:58 下午 GMT+08:00: DOUBAO_WEBVIEW: 等待页面加载...
六月 12, 2025 12:50:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 开始豆包登录流程...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 手机号: 13876513399
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 验证码输入方式 - 优先手动输入: 是
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 开始豆包登录流程...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 登录状态检查: need_login
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_WEBVIEW: 需要登录，启动豆包登录管理器...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: SMS_READER: 检查短信权限...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: SMS_READER: 短信权限检查通过
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_LOGIN: 登录管理器初始化完成
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_LOGIN: 开始自动登录流程，手机号: 13876513399
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_LOGIN: 验证码输入方式 - 优先手动输入: 是
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_LOGIN: 检测登录状态...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: DOUBAO_LOGIN: 登录状态检测结果:
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: - 有登录按钮: true
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: - 有用户头像: true
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: - 有欢迎文字: true
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:13 下午 GMT+08:00: - 页面内容: 下载豆包桌面端安装豆包AI插件新对话下载应用登录你好，我是豆包window.chat_input_html_loaded=(window.chat_input_html_loaded || perfo...
六月 12, 2025 12:51:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:14 下午 GMT+08:00: DOUBAO_LOGIN: 当前登录状态: 未登录
六月 12, 2025 12:51:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:14 下午 GMT+08:00: DOUBAO_LOGIN: 查找登录按钮...
六月 12, 2025 12:51:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:15 下午 GMT+08:00: DOUBAO_LOGIN: 找到并点击登录按钮: 登录
六月 12, 2025 12:51:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:15 下午 GMT+08:00: DOUBAO_LOGIN: 登录按钮点击成功，等待登录页面加载...
六月 12, 2025 12:51:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:18 下午 GMT+08:00: DOUBAO_LOGIN: 输入手机号: 13876513399
六月 12, 2025 12:51:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:18 下午 GMT+08:00: DOUBAO_LOGIN: 手机号输入成功: 13876513399
六月 12, 2025 12:51:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:19 下午 GMT+08:00: DOUBAO_LOGIN: 勾选同意协议...
六月 12, 2025 12:51:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:20 下午 GMT+08:00: DOUBAO_LOGIN: 同意协议勾选成功，状态: true
六月 12, 2025 12:51:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:20 下午 GMT+08:00: DOUBAO_LOGIN: 点击下一步按钮...
六月 12, 2025 12:51:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:21 下午 GMT+08:00: DOUBAO_LOGIN: 下一步按钮点击成功
六月 12, 2025 12:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:21 下午 GMT+08:00: DOUBAO_LOGIN: 下一步按钮点击成功，等待验证码页面加载...
六月 12, 2025 12:51:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:24 下午 GMT+08:00: DOUBAO_LOGIN: 开始获取短信验证码...
六月 12, 2025 12:51:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:24 下午 GMT+08:00: DOUBAO_LOGIN: 用户偏好设置 - 优先手动输入: 是
六月 12, 2025 12:51:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:24 下午 GMT+08:00: DOUBAO_LOGIN: 用户选择优先手动输入验证码
六月 12, 2025 12:51:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:51:24 下午 GMT+08:00: DOUBAO_LOGIN: 等待用户手动输入验证码...
六月 12, 2025 12:51:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:25 下午 GMT+08:00: DOUBAO_LOGIN: 验证码输入超时
六月 12, 2025 12:52:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:25 下午 GMT+08:00: DOUBAO_WEBVIEW: 豆包登录失败: 验证码输入超时或取消
六月 12, 2025 12:52:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:25 下午 GMT+08:00: DOUBAO_WEBVIEW: 初始化登录失败
六月 12, 2025 12:52:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:25 下午 GMT+08:00: DOUBAO_WEBVIEW: 滚动到页面底部...
六月 12, 2025 12:52:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:25 下午 GMT+08:00: DOUBAO_WEBVIEW: 滚动到页面底部...
六月 12, 2025 12:52:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 检查页面加载状态...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 开始检查页面元素...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 页面元素检查结果:
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: === 输入框检查 ===\n✓ 找到: textarea[data-testid=\"chat_input_input\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[placeholder*=\"发消息\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[placeholder*=\"输入\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[1]\n=== 发送按钮检查 ===\n✓ 找到 5 个: button:last-child\n✓ 找到 1 个: [aria-label*=\"发送\"]\n✓ 找到 8 个: button svg\n✓ 找到 10 个: button\n=== 页面信息 ===\n页面标题: 豆包 - 字节跳动旗下 AI 智能助手\n页面URL: https://www.doubao.com/chat/
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 设置为就绪状态
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 豆包WebView初始化完成
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 发送消息: 分享链接内容：
43 岛民今天玩什么?（咨询版）发布了一篇小红书笔记，快来看吧！ 😆 r1RP9Q...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 检查登录状态...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 已登录，准备发送消息...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:27 下午 GMT+08:00: DOUBAO_WEBVIEW: 暂停10秒，请观察页面是否正常显示...
六月 12, 2025 12:52:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:37 下午 GMT+08:00: DOUBAO_WEBVIEW: 发送前再次检查页面元素...
六月 12, 2025 12:52:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:37 下午 GMT+08:00: DOUBAO_WEBVIEW: 开始检查页面元素...
六月 12, 2025 12:52:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:37 下午 GMT+08:00: DOUBAO_WEBVIEW: 页面元素检查结果:
六月 12, 2025 12:52:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:37 下午 GMT+08:00: === 输入框检查 ===\n✓ 找到: textarea[data-testid=\"chat_input_input\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[placeholder*=\"发消息\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[placeholder*=\"输入\"][0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[0] placeholder=\"发消息、输入 @ 或 / 选择技能\" class=\"semi-input-textarea semi-input-textarea-autosize\"\n✓ 找到: textarea[1]\n=== 发送按钮检查 ===\n✓ 找到 5 个: button:last-child\n✓ 找到 1 个: [aria-label*=\"发送\"]\n✓ 找到 8 个: button svg\n✓ 找到 10 个: button\n=== 页面信息 ===\n页面标题: 豆包 - 字节跳动旗下 AI 智能助手\n页面URL: https://www.doubao.com/chat/
六月 12, 2025 12:52:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:37 下午 GMT+08:00: DOUBAO_WEBVIEW: 尝试坐标点击和模拟输入...
六月 12, 2025 12:52:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:42 下午 GMT+08:00: DOUBAO_WEBVIEW: 输入框操作结果: input_success|✓ 找到豆包专用输入框: data-testid=\"chat_input_input\";找到 2 个textarea;找到 1 个input;找到 0 个contenteditable;使用豆包专用输入框;输入框中心坐标: (67, -514);已点击并聚焦输入框;已设置输入框背景为黄色（视觉反馈）;方法1: 设置value属性;方法2: 逐字符模拟输入完成;方法3: 触发所有相关事件;方法4: React/Vue兼容方式;最终输入值: 分享链接内容：\n43 岛民今天玩什么?（咨询版）发布了一篇小红书笔记，快来看吧！ 😆 r1RP9Q...;✓ 输入验证成功 - 内容已正确输入|COORDS:67,-514
六月 12, 2025 12:52:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:47 下午 GMT+08:00: DOUBAO_WEBVIEW: 查找发送按钮...
六月 12, 2025 12:52:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:47 下午 GMT+08:00: DOUBAO_WEBVIEW: 发送按钮操作结果: button_clicked|选择器 #flow-end-msg-send: 找到 1 个;使用按钮: #flow-end-msg-send[0];发送按钮坐标: (107, 102);已设置发送按钮背景为红色（视觉反馈）;已点击发送按钮|COORDS:107,102
六月 12, 2025 12:52:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:47 下午 GMT+08:00: DOUBAO_WEBVIEW: 消息已发送，等待回复...
六月 12, 2025 12:52:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:47 下午 GMT+08:00: DOUBAO_WEBVIEW: 等待豆包回复...
六月 12, 2025 12:52:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:52 下午 GMT+08:00: DOUBAO_WEBVIEW: 等待5秒后开始检查回复...
六月 12, 2025 12:52:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:52 下午 GMT+08:00: DOUBAO_WEBVIEW: 回复查找调试信息: 找到 0 个AI回复消息;使用备用方法查找AI回复...;✅ 备用方法找到AI回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:52 下午 GMT+08:00: DOUBAO_WEBVIEW: 有效回复还在增长，当前长度: 65
六月 12, 2025 12:52:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:53 下午 GMT+08:00: DOUBAO_WEBVIEW: 回复查找调试信息: 找到 0 个AI回复消息;使用备用方法查找AI回复...;✅ 备用方法找到AI回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:53 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:53 下午 GMT+08:00: DOUBAO_WEBVIEW: 有效回复长度稳定 1 次，长度: 65
六月 12, 2025 12:52:53 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:54 下午 GMT+08:00: DOUBAO_WEBVIEW: 回复查找调试信息: 找到 0 个AI回复消息;使用备用方法查找AI回复...;✅ 备用方法找到AI回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:54 下午 GMT+08:00: DOUBAO_WEBVIEW: 有效回复长度稳定 2 次，长度: 65
六月 12, 2025 12:52:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 回复查找调试信息: 找到 0 个AI回复消息;使用备用方法查找AI回复...;✅ 备用方法找到AI回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 有效回复长度稳定 3 次，长度: 65
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 获取到完整回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京ICP备2023020373号-1...
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: DOUBAO_WEBVIEW: 获取到回复: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: DOUBAO_WEBVIEW: AI回复生成完成，保持WebView打开等待评论发布完成
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: LLM_SERVICE: 豆包AI生成评论成功: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC...
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: NOTE_COMMENT: LLM generated comment from 分享链接: 下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京IC
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: NOTE_COMMENT: Using AI generated or config comments: 1 available
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: COMMENT_MANAGER: Initialized with 1 comments, mode: random
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: COMMENT_MANAGER: Comments: ["下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京ICP备2023020373号-1"]
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: NOTE_COMMENT: Using config comments: 1 available
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:55 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 12, 2025 12:52:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:56 下午 GMT+08:00: DOUBAO_WEBVIEW: 用户点击关闭按钮
六月 12, 2025 12:52:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:56 下午 GMT+08:00: DOUBAO_WEBVIEW: 资源已清理
六月 12, 2025 12:52:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: NOTE_COMMENT: Detected note type: 视频笔记
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: COMMENT_MANAGER: Random comment #1: "下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京ICP备2023020373号-1"
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: NOTE_COMMENT: 执行评论前安全延迟...
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: SAFETY: 执行评论前安全操作...
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:57 下午 GMT+08:00: SAFETY: 执行评论延迟 - 14.1 秒
六月 12, 2025 12:52:57 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:58 下午 GMT+08:00: 停止任务按钮被点击
六月 12, 2025 12:52:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:58 下午 GMT+08:00: MAIN_V3: Stop comment user scraping requested.
六月 12, 2025 12:52:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:59 下午 GMT+08:00: NOTE_COMMENT: 安全控制模块调用失败: JavaException: com.stardust.autojs.runtime.exception.ScriptInterruptedException: null
六月 12, 2025 12:52:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:52:59 下午 GMT+08:00: VIDEO_COMMENT: Starting to publish comment: "下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京ICP备2023020373号-1"
六月 12, 2025 12:52:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: VIDEO_COMMENT: Video container not found (c9y)
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: NOTE_COMMENT: Failed to publish comment in note "出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚": "下载豆包电脑版，体验更强大的 AI 能力问问豆包 · 划词提问 · 截图问答 · 网页速读下载京ICP备2023020373号-1"
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: NOTE_COMMENT: 评论发布失败，关闭豆包WebView
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: DOUBAO_WEBVIEW: 资源已清理
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: NOTE_COMMENT: 豆包WebView已关闭
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: SIMPLE_PROCESS: ✗ 评论发布失败或跳过
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: SIMPLE_PROCESS: 返回搜索结果页...
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: PPACTIONS: 使用传入的笔记类型: image_text
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:02 下午 GMT+08:00: PPACTIONS: 根据传入类型，使用图文笔记返回逻辑。
六月 12, 2025 12:53:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:05 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮失败，尝试全局返回键
六月 12, 2025 12:53:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:07 下午 GMT+08:00: SIMPLE_PROCESS: 图文笔记处理完成，成功: false
六月 12, 2025 12:53:07 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:07 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 岛民今天玩什么?（咨询版）::出了蛇咬身亡事件，三亚的旅游业会衰败吗 #出门旅行注意安全  #三亚...
六月 12, 2025 12:53:07 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:07 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 12, 2025 12:53:07 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/5
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略  #三亚天气现状  #三亚天气穿衣"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "三亚王姐"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "三亚王姐::6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略 "
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 6月10日三亚天气 关注我每天更新#三亚天气  #三亚最新天气  #三亚天气预报  #三亚旅游攻略  #三亚天气现状  #三亚天气穿衣
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 点击笔记失败
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/5
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉一夏海南度假  #三亚  #三亚立才农场"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "海南旅文"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "海南旅文::用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 用陈楚生歌声环绕他的家乡 视频来源三亚旅游发展局 #陈楚生  #陈楚生巡演  #趣玩酷海南  #清凉一夏海南度假  #三亚  #三亚立才农场
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 点击笔记失败
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/5
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略  #跟着本地人  #一定要去的地方  #本地人做的攻"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "海南郑秀晶-"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "海南郑秀晶-::海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 海南这个叫啥？？？好吃死了….. #海南旅游 #万宁美食 #万宁旅游攻略 #万宁美食攻略 #旅游攻略  #跟着本地人  #一定要去的地方  #本地人做的攻
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 点击笔记失败
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 5/5
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴还有美食、交通、避坑等全都是干货✌旅行小白也可以放心抄作业 ✍最"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "海南郑秀晶-"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "海南郑秀晶-::在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴"
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 在万宁待了8年‼给6-8月来的姐妹一些建议 6-8月有想去万宁的姐妹！！这份万宁旅行攻略赶紧码住🐴还有美食、交通、避坑等全都是干货✌旅行小白也可以放心抄作业 ✍最
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 点击笔记失败
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:08 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 12, 2025 12:53:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:11 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 12, 2025 12:53:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Element with ID 'com.xingin.xhs:id/j16' does not exist initially.
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: false
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: MAIN_V3_COMMENTING: Not on a search results page. Stopping task.
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 12:53:14 下午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread finished. Total notes commented: 0
六月 12, 2025 12:53:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
