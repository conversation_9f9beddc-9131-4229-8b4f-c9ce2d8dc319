六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require config.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: config.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require ui.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: ui.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_actions.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_safety_control.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: SAFETY: 安全控制初始化完成 - 今日评论次数: 0
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: xhs_safety_control.js loaded and initialized successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_account_manager.js...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: ACCOUNT: 账号管理器初始化完成 - 账号数量: 0, 当前索引: 0
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: xhs_account_manager.js loaded and initialized successfully.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: UI: Doubao AI enabled
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:32 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 10, 2025 3:21:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: UI: 显示模板 "默认内容分析模板"
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: 配置已加载到UI。
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: UI: 显示模板 "默认内容分析模板"
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:33 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 10, 2025 3:21:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:35 下午 GMT+08:00: MAIN_V3: 已提示用户关闭小红书app
六月 10, 2025 3:21:35 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: 开始任务按钮被点击
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: UI: 提醒用户确保小红书已关闭
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: 开始执行任务，选择的任务类型：采集目标客户 目标客户留痕 
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: 开始搜索笔记，参数：{"keyword":"旅游","sortBy":3,"publishTime":1,"locationDistance":1,"targetRegion":0}
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: MAIN_V3: Received search request: {"keyword":"旅游","sortBy":3,"publishTime":1,"locationDistance":1,"targetRegion":0}
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: PPACTIONS: 开始笔记搜索: 关键字="旅游", 排序=3, 时间=1, 位置=1
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS: 尝试确保App '小红书' 打开并切换到前台，强制重启: false
六月 10, 2025 3:21:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:45 下午 GMT+08:00: UTILS: 启动App '小红书' (com.xingin.xhs)...
六月 10, 2025 3:21:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:45 下午 GMT+08:00: UTILS: App '小红书' 启动命令执行成功。等待应用响应...
六月 10, 2025 3:21:45 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:48 下午 GMT+08:00: UTILS: App '小红书' 已成功切换到前台。
六月 10, 2025 3:21:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:48 下午 GMT+08:00: PPACTIONS: 小红书App已准备好。
六月 10, 2025 3:21:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:49 下午 GMT+08:00: PPACTIONS: 找到首页搜索图标/按钮，点击进入搜索页。
六月 10, 2025 3:21:49 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:52 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 1)...
六月 10, 2025 3:21:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:54 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 2)...
六月 10, 2025 3:21:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:56 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 3)...
六月 10, 2025 3:21:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:56 下午 GMT+08:00: PPACTIONS: 找到搜索框 (方法 3)。Bounds: Rect(228, 114 - 786, 210)
六月 10, 2025 3:21:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:56 下午 GMT+08:00: PPACTIONS: 准备向搜索框输入文本...
六月 10, 2025 3:21:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:56 下午 GMT+08:00: PPACTIONS: 已调用 setText 输入关键字: 旅游
六月 10, 2025 3:21:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:58 下午 GMT+08:00: PPACTIONS: 准备触发搜索。
六月 10, 2025 3:21:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:58 下午 GMT+08:00: PPACTIONS: 步骤A - 尝试确保搜索框或其父控件有焦点...
六月 10, 2025 3:21:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:58 下午 GMT+08:00: PPACTIONS: 点击焦点目标: Rect(228, 114 - 786, 210) 以确保焦点。
六月 10, 2025 3:21:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:59 下午 GMT+08:00: PPACTIONS: 步骤B - 尝试查找并点击页面上的搜索按钮...
六月 10, 2025 3:21:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:59 下午 GMT+08:00: PPACTIONS: 尝试查找搜索确认按钮 (策略: Exact ID 'com.xingin.xhs:id/fce', Text='搜索', ClassName=Button, Clickable)...
六月 10, 2025 3:21:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:59 下午 GMT+08:00: PPACTIONS: 找到潜在搜索按钮。详情: Text: "搜索", Desc: "null", ID: com.xingin.xhs:id/fce, Class: android.widget.Button, Bounds: Rect(942, 96 - 1032, 228), Clickable: true, Visible: true
六月 10, 2025 3:21:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:59 下午 GMT+08:00: PPACTIONS: 按钮可见且可点击，尝试执行 click()...
六月 10, 2025 3:21:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:21:59 下午 GMT+08:00: PPACTIONS: searchConfirmButton.click() 执行成功 (返回true)。等待页面跳转...
六月 10, 2025 3:21:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:04 下午 GMT+08:00: PPACTIONS: 搜索已触发，准备应用筛选条件...
六月 10, 2025 3:22:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:04 下午 GMT+08:00: PPACTIONS: 需要应用排序、发布时间或位置筛选，尝试打开主筛选面板。
六月 10, 2025 3:22:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:04 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDHPY_UserExact)
六月 10, 2025 3:22:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:09 下午 GMT+08:00: PPACTIONS: 未通过策略 "FilterButtonByIDHPY_UserExact" 找到主筛选按钮。
六月 10, 2025 3:22:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:09 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDHPY_Clickable)
六月 10, 2025 3:22:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:09 下午 GMT+08:00: PPACTIONS: 找到主筛选按钮候选 (策略: FilterButtonByIDHPY_Clickable)。详情: Text: "", Desc: "null", ID: com.xingin.xhs:id/hpy, Class: android.widget.Button, Bounds: Rect(822, 228 - 1068, 360), Clickable: true, Visible: true
六月 10, 2025 3:22:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:09 下午 GMT+08:00: PPACTIONS: 主筛选按钮候选可用，将使用此按钮 (策略: FilterButtonByIDHPY_Clickable)。
六月 10, 2025 3:22:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:09 下午 GMT+08:00: PPACTIONS: 点击主筛选按钮: null
六月 10, 2025 3:22:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:12 下午 GMT+08:00: PPACTIONS: Validating if filter panel is open after clicking main filter button...
六月 10, 2025 3:22:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: Panel validation details (selectors include id="com.xingin.xhs:id/e1u", checking for visibility):
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00:   - Sort trigger ('综合'): found, visible: true
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00:   - Publish time option ('发布时间'): not found
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00:   - Location option ('位置距离'): not found
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: Filter panel confirmed open: At least one characteristic element is visible.
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: 在筛选面板内开始应用排序依据: 最多评论
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: 尝试直接查找并点击目标排序选项 "最多评论"
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: 尝试定位排序选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("最多评论")
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: 已定位排序选项 "最多评论"，调用 tryClickInPanel。
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "排序选项 "最多评论"".
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "最多评论", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "排序选项 "最多评论"" 文本元素自身.
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "排序选项 "最多评论"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "排序选项 "最多评论"".
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "排序选项 "最多评论"" 不可见 (true) 或不可点击 (false).
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "排序选项 "最多评论"".
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(807, 502 - 1030, 622), Clickable: true, Visible: true
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "排序选项 "最多评论"" 可见且可点击. 尝试 click().
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "排序选项 "最多评论"" 的父控件 (层级 2).
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 的点击操作已标记为成功.
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:14 下午 GMT+08:00: PPACTIONS: 成功处理点击排序选项 "最多评论".
六月 10, 2025 3:22:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:16 下午 GMT+08:00: PPACTIONS: Delaying after sort option processing (1s).
六月 10, 2025 3:22:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: 尝试应用发布时间: "一天内"
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: 尝试定位发布时间选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("一天内")
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: 已定位发布时间选项 "一天内"，调用 tryClickInPanel。
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "发布时间选项 "一天内"".
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "一天内", Desc: "null", Bounds: Rect(340, 1210 - 484, 1318), Clickable: false, Visible: true
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "发布时间选项 "一天内"" 文本元素自身.
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "发布时间选项 "一天内"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "发布时间选项 "一天内"".
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(340, 1210 - 484, 1318), Clickable: false, Visible: true
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "发布时间选项 "一天内"" 不可见 (true) 或不可点击 (false).
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "发布时间选项 "一天内"".
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1204 - 524, 1324), Clickable: true, Visible: true
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "发布时间选项 "一天内"" 可见且可点击. 尝试 click().
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "发布时间选项 "一天内"" 的父控件 (层级 2).
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 的点击操作已标记为成功.
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:18 下午 GMT+08:00: PPACTIONS: 成功处理点击发布时间选项 "一天内".
六月 10, 2025 3:22:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:20 下午 GMT+08:00: PPACTIONS: Delaying after publish time option processing (1s).
六月 10, 2025 3:22:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: 尝试应用位置距离: "同城"
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: 尝试定位位置距离选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("同城")
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: 已定位位置距离选项 "同城"，调用 tryClickInPanel。
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "位置距离选项 "同城"".
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "同城", Desc: "null", Bounds: Rect(361, 1756 - 463, 1864), Clickable: false, Visible: true
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "位置距离选项 "同城"" 文本元素自身.
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "位置距离选项 "同城"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "位置距离选项 "同城"".
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(361, 1756 - 463, 1864), Clickable: false, Visible: true
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "位置距离选项 "同城"" 不可见 (true) 或不可点击 (false).
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "位置距离选项 "同城"".
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1750 - 524, 1870), Clickable: true, Visible: true
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "位置距离选项 "同城"" 可见且可点击. 尝试 click().
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "位置距离选项 "同城"" 的父控件 (层级 2).
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 的点击操作已标记为成功.
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:22 下午 GMT+08:00: PPACTIONS: 成功处理点击位置距离选项 "同城".
六月 10, 2025 3:22:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:24 下午 GMT+08:00: PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。
六月 10, 2025 3:22:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:25 下午 GMT+08:00: PPACTIONS: “收起”文本元素 (id="e1u" and text="收起") 未找到。
六月 10, 2025 3:22:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:25 下午 GMT+08:00: PPACTIONS: “收起”按钮交互失败或未找到，将使用 back() 关闭筛选面板。
六月 10, 2025 3:22:25 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:27 下午 GMT+08:00: PPACTIONS: 尝试额外方法 - 点击筛选面板外的区域
六月 10, 2025 3:22:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:29 下午 GMT+08:00: PPACTIONS: 点击屏幕顶部区域后，筛选面板似乎已关闭
六月 10, 2025 3:22:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:29 下午 GMT+08:00: PPACTIONS: 笔记搜索和筛选操作流程结束。
六月 10, 2025 3:22:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:29 下午 GMT+08:00: MAIN_V3: 搜索成功。 搜索和筛选流程已执行
六月 10, 2025 3:22:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '筛选'
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(864, 266 - 960, 321)
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: 搜索完成，开始采集目标客户
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: Start comment user scraping requested.
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: Initialized states for comment scraping.
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: 目标客户留痕(点赞)功能已启用，将在评论采集过程中自动执行
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: 评论采集线程已启动。
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: 开始评论采集循环。
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: 采集配置 - 关键词: 求,想,多少, 目标区域: 不限
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '筛选'
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(864, 266 - 960, 321)
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: MAIN_V3: 开始处理当前屏幕内的所有笔记...
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 找到 4 个笔记容器
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 处理容器 1/4
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！..."
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 作者: "小罗老师日常"
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 评论数: 14
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了", 作者: "小罗老师日常", 评论数: 14, signature: "小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打"
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 处理容器 2/4
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想..."
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 作者: "早饭了没"
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 评论数: 12
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一晚，选到了超级满意的民宿，满足了我对海岛生活的所有幻想", 作者: "早饭了没", 评论数: 12, signature: "早饭了没::西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一"
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 处理容器 3/4
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R..."
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 作者: "青青芒果"
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:32 下午 GMT+08:00: PPACTIONS: 评论数: 11
六月 10, 2025 3:22:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体验一下）设备自带让教练帮拍的，参数瞎调", 作者: "青青芒果", 评论数: 11, signature: "青青芒果::蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 处理容器 4/4
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，..."
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 作者: "山风不停处"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 评论数: 8
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地热门的的美食，不看价格，但是我会反复对比找出性价比最高", 作者: "山风不停处", 评论数: 8, signature: "山风不停处::消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 提取完成，找到 4 个有效笔记
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了", 作者: "小罗老师日常", 评论数: 14, signature: "小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一晚，选到了超级满意的民宿，满足了我对海岛生活的所有幻想", 作者: "早饭了没", 评论数: 12, signature: "早饭了没::西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体验一下）设备自带让教练帮拍的，参数瞎调", 作者: "青青芒果", 评论数: 11, signature: "青青芒果::蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地热门的的美食，不看价格，但是我会反复对比找出性价比最高", 作者: "山风不停处", 评论数: 8, signature: "山风不停处::消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地"
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: MAIN_V3: 找到 4 个笔记，开始采集
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 0
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: MAIN_V3: 笔记未处理过，继续处理
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:33 下午 GMT+08:00: MAIN_V3: 采集笔记: 强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了
六月 10, 2025 3:22:33 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:37 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 10, 2025 3:22:37 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: MAIN_V3: 成功进入笔记详情页，开始采集评论
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: SIMPLE: 开始采集评论，笔记标题: 强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: SIMPLE: 目标区域: 不限
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:38 下午 GMT+08:00: SIMPLE: 滚动到评论区...
六月 10, 2025 3:22:38 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 0
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="求个推荐 昨天 17:01 河北 回复", nickname="蛋黄酥"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复..."
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 蛋黄酥
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="这个导游哦[向右R]@大炎带你游Beijing   
昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 小罗老师日常 - "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..."
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="求 昨天 17:09 广东 回复", nickname="小甜睡不饱"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 小甜睡不饱 - "求 昨天 17:09 广东 回复..."
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 小甜睡不饱
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小甜睡不饱 - "求 昨天 17:09 广东 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="蛋黄酥", content="求个推荐 昨天 17:01 河北 回复", matchesKeyword=true
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[0] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="这个导游哦[向右R]@大炎带你游Beijing   
昨天 ...", matchesKeyword=false
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[1] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小甜睡不饱", content="求 昨天 17:09 广东 回复", matchesKeyword=true
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[2] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[3] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[4] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[5] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[6] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: DEBUG - allComments[7] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:42 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 8 条评论
六月 10, 2025 3:22:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:43 下午 GMT+08:00: SIMPLE: 本次采集到 8 条新评论，继续滚动寻找更多评论...
六月 10, 2025 3:22:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:43 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 1 次)...
六月 10, 2025 3:22:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 8
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 8
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="求个推荐 昨天 17:01 河北 回复", nickname="蛋黄酥"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复..."
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 蛋黄酥
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="这个导游哦[向右R]@大炎带你游Beijing   
昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 小罗老师日常 - "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..."
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="求 昨天 17:09 广东 回复", nickname="小甜睡不饱"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 小甜睡不饱 - "求 昨天 17:09 广东 回复..."
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 小甜睡不饱
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小甜睡不饱 - "求 昨天 17:09 广东 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="蛋黄酥", content="求个推荐 昨天 17:01 河北 回复", matchesKeyword=true
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[8] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="这个导游哦[向右R]@大炎带你游Beijing   
昨天 ...", matchesKeyword=false
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[9] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小甜睡不饱", content="求 昨天 17:09 广东 回复", matchesKeyword=true
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[10] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[11] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[12] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[13] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[14] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: DEBUG - allComments[15] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:46 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 16 条评论
六月 10, 2025 3:22:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:47 下午 GMT+08:00: SIMPLE: 本次采集到 8 条新评论，继续滚动寻找更多评论...
六月 10, 2025 3:22:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:47 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 2 次)...
六月 10, 2025 3:22:47 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 16
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 16
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="求个推荐 昨天 17:01 河北 回复", nickname="蛋黄酥"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复..."
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 蛋黄酥
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 蛋黄酥 - "求个推荐 昨天 17:01 河北 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="这个导游哦[向右R]@大炎带你游Beijing   
昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 小罗老师日常 - "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..."
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="求 昨天 17:09 广东 回复", nickname="小甜睡不饱"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 小甜睡不饱 - "求 昨天 17:09 广东 回复..."
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 小甜睡不饱
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小甜睡不饱 - "求 昨天 17:09 广东 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="蛋黄酥", content="求个推荐 昨天 17:01 河北 回复", matchesKeyword=true
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[16] = {"nickname":"蛋黄酥","content":"求个推荐 昨天 17:01 河北 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965712},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="这个导游哦[向右R]@大炎带你游Beijing   
昨天 ...", matchesKeyword=false
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[17] = {"nickname":"小罗老师日常","content":"这个导游哦[向右R]@大炎带你游Beijing   \n昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小甜睡不饱", content="求 昨天 17:09 广东 回复", matchesKeyword=true
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[18] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[19] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[20] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[21] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[22] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: DEBUG - allComments[23] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:50 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 24 条评论
六月 10, 2025 3:22:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:51 下午 GMT+08:00: SIMPLE: 本次采集到 8 条新评论，继续滚动寻找更多评论...
六月 10, 2025 3:22:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:51 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 3 次)...
六月 10, 2025 3:22:51 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 24
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 24
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 找到 9 个评论内容元素和 9 个昵称元素
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="求 昨天 17:09 广东 回复", nickname="小甜睡不饱"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 小甜睡不饱 - "求 昨天 17:09 广东 回复..."
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 小甜睡不饱
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 小甜睡不饱 - "求 昨天 17:09 广东 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="求推荐一下 13分钟前  江苏 回复", nickname="风轻扬夏未央"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复..."
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 风轻扬夏未央
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="推荐一下 昨天 17:09 安徽 回复", nickname="一颗星"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 一颗星 - "推荐一下 昨天 17:09 安徽 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 评论元素 #9: commentText="发了 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 提取到评论 #9: 小罗老师日常 - "发了 昨天 17:19 海南 回复"
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 本次提取到 9 条评论
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小甜睡不饱", content="求 昨天 17:09 广东 回复", matchesKeyword=true
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[24] = {"nickname":"小甜睡不饱","content":"求 昨天 17:09 广东 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965684},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[25] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[26] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[27] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[28] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[29] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="风轻扬夏未央", content="求推荐一下 13分钟前  江苏 回复", matchesKeyword=true
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[30] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一颗星", content="推荐一下 昨天 17:09 安徽 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[31] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - newComments[8] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="发了 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: DEBUG - allComments[32] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:54 下午 GMT+08:00: SIMPLE: 本次新增 9 条评论，当前总计 33 条评论
六月 10, 2025 3:22:54 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:55 下午 GMT+08:00: SIMPLE: 本次采集到 9 条新评论，继续滚动寻找更多评论...
六月 10, 2025 3:22:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:55 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 4 次)...
六月 10, 2025 3:22:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 33
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 33
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="求推荐一下 13分钟前  江苏 回复", nickname="风轻扬夏未央"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复..."
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 风轻扬夏未央
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="推荐一下 昨天 17:09 安徽 回复", nickname="一颗星"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 一颗星 - "推荐一下 昨天 17:09 安徽 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="发了 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "发了 昨天 17:19 海南 回复"
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[33] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[34] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[35] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[36] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[37] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="风轻扬夏未央", content="求推荐一下 13分钟前  江苏 回复", matchesKeyword=true
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[38] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一颗星", content="推荐一下 昨天 17:09 安徽 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[39] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="发了 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: DEBUG - allComments[40] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 41 条评论
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 检测到底部提示 '- 到底了 -'（第1次），进行最后一次评论采集确认...
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 底部检测时发现 8 条新评论，继续滚动一次确保完全采集...
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:22:58 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 5 次)...
六月 10, 2025 3:22:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 41
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:01 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:23:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 41
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="求推荐一下 13分钟前  江苏 回复", nickname="风轻扬夏未央"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复..."
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 风轻扬夏未央
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="推荐一下 昨天 17:09 安徽 回复", nickname="一颗星"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 一颗星 - "推荐一下 昨天 17:09 安徽 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="发了 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "发了 昨天 17:19 海南 回复"
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[41] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[42] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[43] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[44] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[45] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="风轻扬夏未央", content="求推荐一下 13分钟前  江苏 回复", matchesKeyword=true
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[46] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一颗星", content="推荐一下 昨天 17:09 安徽 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[47] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="发了 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: DEBUG - allComments[48] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 49 条评论
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 检测到底部提示 '- 到底了 -'（第2次），进行最后一次评论采集确认...
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 底部检测时发现 8 条新评论，继续滚动一次确保完全采集...
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:02 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 6 次)...
六月 10, 2025 3:23:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 49
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 49
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "蛋黄酥", content: "求个推荐 昨天 17:01 河北 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "小罗老师日常", content: "这个导游哦[向右R]@大炎带你游Beijing   
昨天 ..." }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "小甜睡不饱", content: "求 昨天 17:09 广东 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "小罗老师日常", content: "好的 昨天 17:19 海南 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "一杯冰美式", content: "三个大人一小孩 昨天 17:12 江西 回复" }
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 找到 8 个评论内容元素和 8 个昵称元素
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="三个大人一小孩 昨天 17:12 江西 回复", nickname="一杯冰美式"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 一杯冰美式 - "三个大人一小孩 昨天 17:12 江西 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="求推谢谢 昨天 17:14 北京 回复", nickname="青海小美"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 青海小美 - "求推谢谢 昨天 17:14 北京 回复..."
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 青海小美
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 青海小美 - "求推谢谢 昨天 17:14 北京 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="好的 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 小罗老师日常 - "好的 昨天 17:19 海南 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #6: commentText="求推荐一下 13分钟前  江苏 回复", nickname="风轻扬夏未央"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "求": 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复..."
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论匹配关键字，标记需要获取用户信息: 风轻扬夏未央
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #6: 风轻扬夏未央 - "求推荐一下 13分钟前  江苏 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #7: commentText="推荐一下 昨天 17:09 安徽 回复", nickname="一颗星"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #7: 一颗星 - "推荐一下 昨天 17:09 安徽 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 评论元素 #8: commentText="发了 昨天 17:19 海南 回复", nickname="小罗老师日常"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 提取到评论 #8: 小罗老师日常 - "发了 昨天 17:19 海南 回复"
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 本次提取到 8 条评论
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[49] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一杯冰美式", content="三个大人一小孩 昨天 17:12 江西 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[50] = {"nickname":"一杯冰美式","content":"三个大人一小孩 昨天 17:12 江西 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[51] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="青海小美", content="求推谢谢 昨天 17:14 北京 回复", matchesKeyword=true
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[52] = {"nickname":"青海小美","content":"求推谢谢 昨天 17:14 北京 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965577},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="好的 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[53] = {"nickname":"小罗老师日常","content":"好的 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[5] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="风轻扬夏未央", content="求推荐一下 13分钟前  江苏 回复", matchesKeyword=true
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[54] = {"nickname":"风轻扬夏未央","content":"求推荐一下 13分钟前  江苏 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false,"needsUserInfo":true,"nicknameElement":{"mDepth":15,"mIndexInParent":0,"mStackTrace":"","mInfo":{"mSealed":true,"mSourceNodeId":-4294965542},"mParentVirtualDescendantId":-1,"mVirtualDescendantId":-1}}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[6] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一颗星", content="推荐一下 昨天 17:09 安徽 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[55] = {"nickname":"一颗星","content":"推荐一下 昨天 17:09 安徽 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - newComments[7] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="小罗老师日常", content="发了 昨天 17:19 海南 回复", matchesKeyword=false
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: DEBUG - allComments[56] = {"nickname":"小罗老师日常","content":"发了 昨天 17:19 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false,"needsUserInfo":false}, 内存地址: [object Object]
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 本次新增 8 条评论，当前总计 57 条评论
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 检测到底部提示 '- 到底了 -'（第3次），进行最后一次评论采集确认...
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 已连续3次检测到底部，强制结束采集
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 完成评论采集，原因: 连续3次检测到底部，强制结束
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 共采集到 57 条评论，其中 18 条匹配关键字
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:05 下午 GMT+08:00: SIMPLE: 开始获取 18 条匹配评论的用户信息...
六月 10, 2025 3:23:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: USER_PROFILE: 小红书用户信息采集模块加载完毕
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 首次处理用户 蛋黄酥，开始获取用户信息
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: USER_PROFILE: 开始获取用户信息...
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: USER_PROFILE: 准备获取用户 "蛋黄酥" 的信息
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: USER_PROFILE: 关联评论: "求个推荐 昨天 17:01 河北 回复"
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: USER_PROFILE: 点击昵称进入用户页面...
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: ERROR: USER_PROFILE: 点击昵称失败
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 用户 蛋黄酥 已处理过，跳过重复获取
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: SIMPLE: 等待 1 个用户信息获取完成（最多等待30秒）...
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: MAIN_V3: 笔记已标记为已处理: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: PPACTIONS: 使用传入的笔记类型: image_text
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: PPACTIONS: 根据传入类型，使用图文笔记返回逻辑。
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:06 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮点击成功
六月 10, 2025 3:23:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:10 下午 GMT+08:00: MAIN_V3: 当前屏幕处理完成。处理了 0 个笔记，采集了 0 条评论
六月 10, 2025 3:23:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:13 下午 GMT+08:00: PPACTIONS: 未检测到底部提示，页面可能还有更多内容。
六月 10, 2025 3:23:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:13 下午 GMT+08:00: MAIN_V3: 当前屏幕处理完成，尝试下滚加载更多笔记...
六月 10, 2025 3:23:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:13 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 10, 2025 3:23:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:16 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 10, 2025 3:23:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:16 下午 GMT+08:00: MAIN_V3: 下滚成功，等待新内容加载后继续处理...
六月 10, 2025 3:23:16 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:18 下午 GMT+08:00: MAIN_V3: 开始处理当前屏幕内的所有笔记...
六月 10, 2025 3:23:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:18 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 10, 2025 3:23:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 找到 8 个笔记容器
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 1/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "小罗老师日常"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 14
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了", 作者: "小罗老师日常", 评论数: 14, signature: "小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 2/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "早饭了没"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 12
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一晚，选到了超级满意的民宿，满足了我对海岛生活的所有幻想", 作者: "早饭了没", 评论数: 12, signature: "早饭了没::西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 3/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "青青芒果"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 11
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体验一下）设备自带让教练帮拍的，参数瞎调", 作者: "青青芒果", 评论数: 11, signature: "青青芒果::蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 4/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "山风不停处"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地热门的的美食，不看价格，但是我会反复对比找出性价比最高", 作者: "山风不停处", 评论数: 8, signature: "山风不停处::消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 5/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "山风不停处"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 7
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略之后就兴冲冲的带我去打卡。但是每次刚到，我刚想好", 作者: "山风不停处", 评论数: 7, signature: "山风不停处::出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 6/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "嗜血樂少666"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 6
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子一起去 大概是6.22号-6.24号", 作者: "嗜血樂少666", 评论数: 6, signature: "嗜血樂少666::有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 7/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜..."
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 作者: "王宝妞"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 评论数: 4
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻略速🐎！ 🌙 夜市的正确打开方式 *⏰ 傍晚6点后开逛！越夜越嗨", 作者: "王宝妞", 评论数: 4, signature: "王宝妞::推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻"
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 处理容器 8/8
六月 10, 2025 3:23:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:19 下午 GMT+08:00: PPACTIONS: 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋..."
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 作者: "狗狗吃饭🍚"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 评论数: 5
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是大学生嘿嘿 计划是从西马➡️新加坡➡️东马➡️印", 作者: "狗狗吃饭🍚", 评论数: 5, signature: "狗狗吃饭🍚::大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 提取完成，找到 8 个有效笔记
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打不了广告，这是我玩后真实感受分享，和大家谈谈关于旅游团的实话了", 作者: "小罗老师日常", 评论数: 14, signature: "小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一晚，选到了超级满意的民宿，满足了我对海岛生活的所有幻想", 作者: "早饭了没", 评论数: 12, signature: "早饭了没::西岛CAHWAM⛱️i人的治愈之地…… 来西岛几次啦，这次想感受一下西岛的慢生活，所以打算在西岛住一"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体验一下）设备自带让教练帮拍的，参数瞎调", 作者: "青青芒果", 评论数: 11, signature: "青青芒果::蜈支洲岛水肺潜🤿 感觉蜈支洲岛的潜点没什么东西可看[哭惹R]太腻了（只想拍照出片的妹妹还是可以去体"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地热门的的美食，不看价格，但是我会反复对比找出性价比最高", 作者: "山风不停处", 评论数: 8, signature: "山风不停处::消费观差距大的人根本没法一起旅游 住的时候，她觉得酒店干净，我觉得青旅便宜。吃饭的时候，她喜欢吃当地"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略之后就兴冲冲的带我去打卡。但是每次刚到，我刚想好", 作者: "山风不停处", 评论数: 7, signature: "山风不停处::出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[5] - 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子一起去 大概是6.22号-6.24号", 作者: "嗜血樂少666", 评论数: 6, signature: "嗜血樂少666::有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[6] - 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻略速🐎！ 🌙 夜市的正确打开方式 *⏰ 傍晚6点后开逛！越夜越嗨", 作者: "王宝妞", 评论数: 4, signature: "王宝妞::推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: PPACTIONS: 数组[7] - 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是大学生嘿嘿 计划是从西马➡️新加坡➡️东马➡️印", 作者: "狗狗吃饭🍚", 评论数: 5, signature: "狗狗吃饭🍚::大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是"
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 找到 8 个笔记，开始采集
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我也没有粉丝，也打
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 笔记已处理过，跳过: 小罗老师日常::强烈推荐我报的这家北京纯玩团（不是托！） 不是托不是托！！！首先我是一个小学老师，我...
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:20 下午 GMT+08:00: MAIN_V3: 当前屏幕处理完成。处理了 0 个笔记，采集了 0 条评论
六月 10, 2025 3:23:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:23 下午 GMT+08:00: PPACTIONS: 未检测到底部提示，页面可能还有更多内容。
六月 10, 2025 3:23:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:23 下午 GMT+08:00: MAIN_V3: 当前屏幕处理完成，尝试下滚加载更多笔记...
六月 10, 2025 3:23:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:23 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 10, 2025 3:23:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:26 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 10, 2025 3:23:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:26 下午 GMT+08:00: MAIN_V3: 下滚成功，等待新内容加载后继续处理...
六月 10, 2025 3:23:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:28 下午 GMT+08:00: MAIN_V3: 开始处理当前屏幕内的所有笔记...
六月 10, 2025 3:23:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:28 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 10, 2025 3:23:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 找到 5 个笔记容器
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 处理容器 1/5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有..."
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 作者: "山风不停处"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 评论数: 7
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略之后就兴冲冲的带我去打卡。但是每次刚到，我刚想好", 作者: "山风不停处", 评论数: 7, signature: "山风不停处::出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 处理容器 2/5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的..."
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 作者: "嗜血樂少666"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 评论数: 6
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子一起去 大概是6.22号-6.24号", 作者: "嗜血樂少666", 评论数: 6, signature: "嗜血樂少666::有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 处理容器 3/5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜..."
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 作者: "王宝妞"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 评论数: 4
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻略速🐎！ 🌙 夜市的正确打开方式 *⏰ 傍晚6点后开逛！越夜越嗨", 作者: "王宝妞", 评论数: 4, signature: "王宝妞::推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 处理容器 4/5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋..."
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 作者: "狗狗吃饭🍚"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 评论数: 5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是大学生嘿嘿 计划是从西马➡️新加坡➡️东马➡️印", 作者: "狗狗吃饭🍚", 评论数: 5, signature: "狗狗吃饭🍚::大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 处理容器 5/5
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 标题: "🌟🌈【🌴海南槟榔谷🎉毕业旅行✨必打卡！👨‍👩‍?..."
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 作者: "du拉拉"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 评论数: 4
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "🌟🌈【🌴海南槟榔谷🎉毕业旅行✨必打卡！👨‍👩‍👧‍👦暑期亲子游👩‍👩‍👧‍👦🚗】🚗 🔥"毕", 作者: "du拉拉", 评论数: 4, signature: "du拉拉::🌟🌈【🌴海南槟榔谷🎉毕业旅行✨必打卡！👨‍👩‍👧‍👦暑期亲子游👩‍👩‍👧‍👦"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 提取完成，找到 5 个有效笔记
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略之后就兴冲冲的带我去打卡。但是每次刚到，我刚想好", 作者: "山风不停处", 评论数: 7, signature: "山风不停处::出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子一起去 大概是6.22号-6.24号", 作者: "嗜血樂少666", 评论数: 6, signature: "嗜血樂少666::有木有西北搭子 本人02女大学生infp 自己去青甘大环线的话要多付大约800多的房费所以想找个搭子"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻略速🐎！ 🌙 夜市的正确打开方式 *⏰ 傍晚6点后开逛！越夜越嗨", 作者: "王宝妞", 评论数: 4, signature: "王宝妞::推荐给6️⃣、7️⃣月份来三亚旅游的宝子们！！ 水果海鲜便宜哭，裙子饰品白菜价！预算有限也能嗨玩！攻"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是大学生嘿嘿 计划是从西马➡️新加坡➡️东马➡️印", 作者: "狗狗吃饭🍚", 评论数: 5, signature: "狗狗吃饭🍚::大学生出国游找两个女生搭子 如题！大概7.6左右！目前我跟朋友两个女生！想着四个人出行方便一点。都是"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "🌟🌈【🌴海南槟榔谷🎉毕业旅行✨必打卡！👨‍👩‍👧‍👦暑期亲子游👩‍👩‍👧‍👦🚗】🚗 🔥"毕", 作者: "du拉拉", 评论数: 4, signature: "du拉拉::🌟🌈【🌴海南槟榔谷🎉毕业旅行✨必打卡！👨‍👩‍👧‍👦暑期亲子游👩‍👩‍👧‍👦"
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: MAIN_V3: 找到 5 个笔记，开始采集
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: MAIN_V3: 检查笔记去重 - signature: 山风不停处::出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: MAIN_V3: 当前已处理笔记数量: 1
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: MAIN_V3: 笔记未处理过，继续处理
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 10, 2025 3:23:29 下午 GMT+08:00: MAIN_V3: 采集笔记: 出去旅游朋友只打卡怎么办？ 我们计划去重庆旅游两天，因为只有两天，能逛的地方很少，朋友在网上做好攻略之后就兴冲冲的带我去打卡。但是每次刚到，我刚想好
六月 10, 2025 3:23:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
