六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 4) for "位置距离选项 "同城"" 不可见 (true) 或不可点击 (false).
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: [tryClickInPanel] 未能通过查找父控件点击 "位置距离选项 "同城"".
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 3: 前两步均失败 for "位置距离选项 "同城"". 最后尝试直接点击原始文本元素.
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: WARN: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 原始文本元素在“最后尝试”时不可见. 无法点击.
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: WARN: PPACTIONS: [tryClickInPanel] 所有点击尝试对 "位置距离选项 "同城"" 均失败.
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: WARN: PPACTIONS: 未能成功点击位置距离选项 "同城" 通过任何方法.
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: 已定位收起按钮 (ID: bad)，尝试点击。
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:11 上午 GMT+08:00: PPACTIONS: 成功点击收起按钮。
六月 12, 2025 10:13:11 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:13 上午 GMT+08:00: PPACTIONS: 笔记搜索和筛选操作流程结束。
六月 12, 2025 10:13:13 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:13 上午 GMT+08:00: MAIN_V3: 搜索成功。 搜索和筛选流程已执行
六月 12, 2025 10:13:13 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: 搜索完成，开始笔记截流(评论笔记)
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: MAIN_V3_COMMENTING: startNoteCommenting task initiated by UI.
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread started.
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/j16'
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(822, 228 - 1068, 360)
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 找到 4 个笔记容器
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 处理容器 1/4
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 标题: "和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，..."
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 作者: "Pilot"
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看", 作者: "Pilot", 评论数: 0, signature: "Pilot::和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方"
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:16 上午 GMT+08:00: PPACTIONS: 处理容器 2/4
六月 12, 2025 10:13:16 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 标题: "想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游..."
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 作者: "江江很幸运"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游 #昆明 #去有风的 想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游  #昆明 #去有风的地方", 作者: "江江很幸运", 评论数: 0, signature: "江江很幸运::想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游 #昆明 #去有风的 想去云南玩，大理、"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 处理容器 3/4
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 标题: "live以为呼伦贝尔已经够美了，直到我去了… 额尔古纳河右岸..."
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 作者: "史蒂芬尼莽"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "live以为呼伦贝尔已经够美了，直到我去了… 额尔古纳河右岸，原来生命是可以这样流逝的😭 在呼伦贝尔的封神夏天终于理解：原来油画的场景时真实存在的！这个夏天值得用一", 作者: "史蒂芬尼莽", 评论数: 0, signature: "史蒂芬尼莽::live以为呼伦贝尔已经够美了，直到我去了… 额尔古纳河右岸，原来生命是可以这样流逝的😭 在呼伦贝"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 处理容器 4/4
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 标题: "和男朋友去新疆9000够吗？ 托勿扰！！！ #先旅游还是先攒..."
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 作者: "厌离"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 评论数: 0
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "和男朋友去新疆9000够吗？ 托勿扰！！！ #先旅游还是先攒钱  #情侣旅行  #旅游推荐  #新疆", 作者: "厌离", 评论数: 0, signature: "厌离::和男朋友去新疆9000够吗？ 托勿扰！！！ #先旅游还是先攒钱  #情侣旅行  #旅游推荐  #新疆"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 提取完成，找到 4 个有效笔记
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看", 作者: "Pilot", 评论数: 0, signature: "Pilot::和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游 #昆明 #去有风的 想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游  #昆明 #去有风的地方", 作者: "江江很幸运", 评论数: 0, signature: "江江很幸运::想去云南玩，大理、昆明、丽江、香格里拉，舍弃哪一个#云南旅游 #昆明 #去有风的 想去云南玩，大理、"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "live以为呼伦贝尔已经够美了，直到我去了… 额尔古纳河右岸，原来生命是可以这样流逝的😭 在呼伦贝尔的封神夏天终于理解：原来油画的场景时真实存在的！这个夏天值得用一", 作者: "史蒂芬尼莽", 评论数: 0, signature: "史蒂芬尼莽::live以为呼伦贝尔已经够美了，直到我去了… 额尔古纳河右岸，原来生命是可以这样流逝的😭 在呼伦贝"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "和男朋友去新疆9000够吗？ 托勿扰！！！ #先旅游还是先攒钱  #情侣旅行  #旅游推荐  #新疆", 作者: "厌离", 评论数: 0, signature: "厌离::和男朋友去新疆9000够吗？ 托勿扰！！！ #先旅游还是先攒钱  #情侣旅行  #旅游推荐  #新疆"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 找到 4 个笔记，开始处理
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/4
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "Pilot"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 笔记评论数: 0
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "Pilot::和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方"
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: SAFETY: 开始执行综合安全检查...
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: SAFETY: 每日评论次数检查 - 当前: 0, 限制: 15, 结果: 通过
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: SAFETY: 综合安全检查通过
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 安全检查通过
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:17 上午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看
六月 12, 2025 10:13:17 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:20 上午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: image_text
六月 12, 2025 10:13:20 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: MAIN_V3_COMMENTING: 调用图文笔记处理模块
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: SIMPLE_PROCESS: 开始处理图文笔记: 和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: SIMPLE_PROCESS: 开始评论发布...
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "和女朋友旅游闹崩了，谁的问题？ 最近和女朋友去南昌玩了一趟，结果闹得非常不愉快，现在她把我所有联系方式都拉黑了。我心里也很憋屈，想请各位帮忙捋一捋，看看"
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"","commentMode":"random","enableCommenting":true,"enableDeduplication":false,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认内容分析模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"默认链接分析模板","content":"请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【参考话术示例 - 我期望的风格（正例） - 强调去过、有经验、小伙伴开心】：\n\n\"哇塞，这里太美了！上次和姐妹们去也挑战了这儿，还好提前做了功课，大家都玩得很尽兴！🥰\"\n\n\"看得口水直流！上次和吃货小分队也是这么扫街的，个个都吃撑了表示超满足！🤤\"\n\n\"这海水太治愈了！我们上次一行人也去了个类似秘境，回来后大家都说不虚此行！😌\"\n解读：明确“去过”（我们上次一行人也去了个类似秘境），暗示“小伙伴开心”（回来后大家都说不虚此行）。\n\n\"这路线太赞了！上次和队友们一起拿下，风景超绝，大家都说下次还想这么玩！💪\"\n\n\"博主好会规划！我们之前一帮朋友去，也是这么串联景点的，大家都觉得特别值！🤩\"\n\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512,"safetyMinCommentCount":0,"safetyCommentDelayMin":5,"safetyCommentDelayMax":15,"safetyMaxCommentsPerAccount":15,"enableMultiAccount":false,"accountList":"","autoSwitchOnLimit":true}
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_COMMENT: AI评论已启用，开始生成...
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_COMMENT: Using share link method for LLM analysis
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: SHARE_LINK: 开始复制图文笔记分享链接
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:21 上午 GMT+08:00: SHARE_LINK: 点击图文笔记转发按钮
六月 12, 2025 10:13:21 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 找到desc='复制链接'的元素
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 分析desc元素的层级结构
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: desc元素信息 - class: android.widget.Button, clickable: false
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: desc元素有 1 个子元素
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 子元素0 - class: android.view.ViewGroup, clickable: true
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 找到可点击的ViewGroup子元素
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 最终找到的可点击元素信息:
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - ID: com.xingin.xhs:id/jb0
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - Class: android.view.ViewGroup
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - Desc: null
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - Text: null
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - Clickable: true
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: - Bounds: {"bottom":2103,"left":501,"right":699,"top":1833}
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 已清空剪贴板
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 点击复制链接按钮
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 直接点击成功
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:23 上午 GMT+08:00: SHARE_LINK: 等待复制完成...
六月 12, 2025 10:13:23 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:25 上午 GMT+08:00: SHARE_LINK: 使用悬浮窗方式获取剪贴板内容
六月 12, 2025 10:13:25 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: SHARE_LINK: 悬浮窗方式获取到剪贴板内容: 36 Pilot发布了一篇小红书笔记，快来看吧！ 😆 weqyKnvZNQJnU33 😆 http://xhslink.com/a/DDwDHxeCc2Jeb，复制本条信息，打开【小红书】App查看精彩内容！
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: SHARE_LINK: 剪贴板内容: 36 Pilot发布了一篇小红书笔记，快来看吧！ 😆 weqyKnvZNQJnU33 😆 http://xhslink.com/a/DDwDHxeCc2Jeb，复制本条信息，打开【小红书】App查看精彩内容！
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: NOTE_COMMENT: Share link copied successfully: 36 Pilot发布了一篇小红书笔记，快来看吧！ 😆 weqyKnvZNQJnU33 😆 htt...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: NOTE_COMMENT: Using LLM prompt template: '旅游'
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: NOTE_COMMENT: 调用AI分析分享链接...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: LLM_SERVICE: 使用豆包AI模式
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: DOUBAO_WEBVIEW: 开始初始化豆包WebView...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: DOUBAO_WEBVIEW: 创建悬浮窗...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: DOUBAO_WEBVIEW: 设置悬浮窗属性...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:26 上午 GMT+08:00: DOUBAO_WEBVIEW: 等待悬浮窗创建完成...
六月 12, 2025 10:13:26 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:29 上午 GMT+08:00: DOUBAO_WEBVIEW: 获取WebView控件...
六月 12, 2025 10:13:29 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:29 上午 GMT+08:00: DOUBAO_WEBVIEW: 配置WebView...
六月 12, 2025 10:13:29 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:30 上午 GMT+08:00: DOUBAO_WEBVIEW: 加载豆包移动版网站...
六月 12, 2025 10:13:30 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:30 上午 GMT+08:00: DOUBAO_WEBVIEW: 等待页面加载...
六月 12, 2025 10:13:30 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: DOUBAO_WEBVIEW: 初始化登录管理器...
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: SMS_READER: 检查短信权限...
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: SMS_READER: 短信权限检查通过
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: DOUBAO_LOGIN: 登录管理器初始化完成
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: DOUBAO_WEBVIEW: 检查登录状态...
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: DOUBAO_LOGIN: 检测登录状态...
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: DOUBAO_LOGIN: 登录状态检测结果:
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: - 有登录按钮: true
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: - 有用户头像: true
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: - 有欢迎文字: true
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:45 上午 GMT+08:00: - 页面内容: 下载豆包桌面端安装豆包AI插件新对话下载应用登录你好，我是豆包window.chat_input_html_loaded=(window.chat_input_html_loaded || perfo...
六月 12, 2025 10:13:45 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_LOGIN: 当前登录状态: 未登录
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_WEBVIEW: 需要登录，开始自动登录流程...
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_WEBVIEW: 验证码输入方式 - 优先手动输入: 否
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_LOGIN: 开始自动登录流程，手机号: 13876513399
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_LOGIN: 验证码输入方式 - 优先手动输入: 否
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_LOGIN: 检测登录状态...
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: DOUBAO_LOGIN: 登录状态检测结果:
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: - 有登录按钮: true
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: - 有用户头像: true
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: - 有欢迎文字: true
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:46 上午 GMT+08:00: - 页面内容: 下载豆包桌面端安装豆包AI插件新对话下载应用登录你好，我是豆包window.chat_input_html_loaded=(window.chat_input_html_loaded || perfo...
六月 12, 2025 10:13:46 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:47 上午 GMT+08:00: DOUBAO_LOGIN: 当前登录状态: 未登录
六月 12, 2025 10:13:47 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:47 上午 GMT+08:00: DOUBAO_LOGIN: 查找登录按钮...
六月 12, 2025 10:13:47 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:47 上午 GMT+08:00: DOUBAO_LOGIN: 找到并点击登录按钮: 登录
六月 12, 2025 10:13:47 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:48 上午 GMT+08:00: DOUBAO_LOGIN: 登录按钮点击成功，等待登录页面加载...
六月 12, 2025 10:13:48 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:51 上午 GMT+08:00: DOUBAO_LOGIN: 输入手机号: 13876513399
六月 12, 2025 10:13:51 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:51 上午 GMT+08:00: DOUBAO_LOGIN: 手机号输入成功: 13876513399
六月 12, 2025 10:13:51 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:52 上午 GMT+08:00: DOUBAO_LOGIN: 勾选同意协议...
六月 12, 2025 10:13:52 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:52 上午 GMT+08:00: DOUBAO_LOGIN: 同意协议勾选成功，状态: true
六月 12, 2025 10:13:52 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:53 上午 GMT+08:00: DOUBAO_LOGIN: 点击下一步按钮...
六月 12, 2025 10:13:53 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:53 上午 GMT+08:00: DOUBAO_LOGIN: 下一步按钮点击成功
六月 12, 2025 10:13:53 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:54 上午 GMT+08:00: DOUBAO_LOGIN: 下一步按钮点击成功，等待验证码页面加载...
六月 12, 2025 10:13:54 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: DOUBAO_LOGIN: 开始获取短信验证码...
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: DOUBAO_LOGIN: 用户偏好设置 - 优先手动输入: 否
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: DOUBAO_LOGIN: 尝试自动获取短信验证码...
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: SMS_READER: 开始等待验证码，超时: 120秒
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: SMS_READER: 将同时启动自动监听和手动输入选项
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: SMS_READER: 启动短信内容观察者...
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: SMS_READER: 短信内容观察者启动成功
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:13:57 上午 GMT+08:00: SMS_READER: 自动短信监听已启动，等待验证码...
六月 12, 2025 10:13:57 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:07 上午 GMT+08:00: SMS_READER: 等待验证码中... (10/120)
六月 12, 2025 10:14:07 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:17 上午 GMT+08:00: SMS_READER: 等待验证码中... (20/120)
六月 12, 2025 10:14:18 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:28 上午 GMT+08:00: SMS_READER: 等待验证码中... (30/120)
六月 12, 2025 10:14:28 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:33 上午 GMT+08:00: DOUBAO_WEBVIEW: 用户点击关闭按钮
六月 12, 2025 10:14:33 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:33 上午 GMT+08:00: DOUBAO_WEBVIEW: 资源已清理
六月 12, 2025 10:14:33 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:38 上午 GMT+08:00: SMS_READER: 等待验证码中... (40/120)
六月 12, 2025 10:14:38 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 12, 2025 10:14:48 上午 GMT+08:00: SMS_READER: 等待验证码中... (50/120)
六月 12, 2025 10:14:48 上午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
