// ui_tabs.js - 选项卡UI系统
"ui";

console.log("UI_TABS: ui_tabs.js module parsed.");

/**
 * 获取选项卡布局XML
 * @returns {string} 选项卡布局的XML字符串
 */
function getTabLayoutXML() {
    console.log("UI_TABS: getTabLayoutXML() called.");
    return `
        <vertical padding="8">
            <text textSize="20sp" textColor="#2196F3" text="小红书自动化助手 v8.0" gravity="center" marginBottom="8dp"/>

            <!-- 重要提示卡片 -->
            <card cardBackgroundColor="#ffeeee" cardCornerRadius="8dp" cardElevation="4dp" margin="8dp">
                <vertical padding="12dp">
                    <text textSize="16sp" textColor="#cc0000" textStyle="bold" text="⚠️ 重要提示" />
                    <text textSize="14sp" textColor="#cc0000" text="使用前请先手动关闭小红书app！" marginTop="4dp" />
                    <text textSize="12sp" textColor="#666666" text="确保从干净状态开始任务，避免元素检测失败" marginTop="2dp" />
                </vertical>
            </card>

            <!-- 选项卡头部 -->
            <horizontal bg="#f5f5f5" padding="4">
                <button id="tab_main" text="主功能" layout_weight="1" textColor="#2196F3"/>
                <button id="tab_settings" text="设置" layout_weight="1" textColor="#666666"/>
                <button id="tab_users" text="用户列表" layout_weight="1" textColor="#666666"/>
            </horizontal>
            
            <!-- 选项卡内容区域 -->
            <ScrollView layout_weight="1">
                <vertical id="tab_content_container">
                    <!-- 主功能选项卡内容 -->
                    <vertical id="tab_main_content">
                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="笔记搜索与筛选:" />
                        <input id="search_keyword" hint="输入笔记关键字"/>
                        <text textSize="14sp" text="排序依据:" />
                        <spinner id="search_sort_by" entries="综合|最新|最多点赞|最多评论|最多收藏"/>
                        <text textSize="14sp" text="发布时间:" />
                        <spinner id="search_publish_time" entries="不限|一天内|一周内|半年内"/>
                        <text textSize="14sp" text="位置距离:" />
                        <spinner id="search_location_distance" entries="不限|同城|附近"/>

                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="任务设置:" />
                        <text textSize="14sp" text="目标客户筛选关键词 (逗号分隔):" />
                        <input id="commentKeywords" hint="例如：求带,想了解,怎么做" />
                        <text textSize="14sp" text="目标客户区域筛选 (可选):" />
                        <spinner id="targetRegion" entries="不限|北京|上海|天津|重庆|河北|山西|内蒙古|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|广西|海南|四川|贵州|云南|西藏|陕西|甘肃|青海|宁夏|新疆"/>

                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="任务类型:" />
                        <checkbox id="task_collect_users" text="采集目标客户" checked="true" />
                        <checkbox id="task_comment_notes" text="笔记截流(评论笔记)" />
                        <checkbox id="task_like_users" text="目标客户留痕(点赞)" />

                        <horizontal marginTop="10dp">
                            <button id="startBtn" text="开始任务" layout_weight="1"/>
                            <button id="stopBtn" text="停止任务" layout_weight="1"/>
                        </horizontal>
                        
                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="任务状态:" />
                        <text id="scrapingStatus" text="状态: 未开始" marginTop="5dp"/>
                        <text id="processedNotesCount" text="已处理笔记: 0" marginTop="5dp"/>
                        <text id="collectedUsersCount" text="已采集用户: 0" marginTop="5dp"/>
                    </vertical>

                    <!-- 设置选项卡内容 (初始隐藏) -->
                    <vertical id="tab_settings_content" visibility="gone">
                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="评论设置 (笔记截流功能):" />
                        <text textSize="14sp" text="自定义评论内容 (每行一条评论):" />
                        <input id="customComments" hint="输入评论内容，每行一条" lines="4" />
                        <text textSize="14sp" text="评论模式:" />
                        <spinner id="commentMode" entries="随机选择|按顺序选择"/>
                        <checkbox id="enableCommentDeduplication" text="避免重复评论 (已评论过的笔记将跳过)" />
                        <checkbox id="enableLlmComments" text="使用AI生成评论" />

                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="AI模型设置:" />
                        <checkbox id="useDoubaoProxy" text="使用豆包AI" />
                        <input id="doubaoPhoneNumber" hint="豆包登录手机号 (例如: ***********)"/>

                        <text textSize="14sp" text="豆包验证码输入方式:" marginTop="8dp" />
                        <horizontal>
                            <checkbox id="doubaoAutoSmsCode" text="优先自动获取短信验证码" checked="true" layout_weight="1"/>
                            <checkbox id="doubaoManualCode" text="优先手动输入验证码" layout_weight="1"/>
                        </horizontal>
                        <text textSize="12sp" textColor="#666666" text="提示：自动获取失败时会自动切换到手动输入" marginTop="2dp"/>

                        <input id="llmApiUrl" hint="通用LLM API 地址 (例如: http://localhost:1234/v1)"/>
                        <input id="llmModelName" hint="LLM 模型名称 (例如: gpt-3.5-turbo)"/>

                        <text textSize="14sp" text="AI分析方式:" marginTop="10dp" />
                        <checkbox id="llmUseContentExtraction" text="提取笔记内容发送给AI" checked="true" />
                        <checkbox id="llmUseShareLink" text="复制分享链接发送给AI" />

                        <text textSize="14sp" text="提示词模板选择:" marginTop="10dp" />
                        <horizontal>
                            <button id="templateSelectorBtn" text="点击选择模板" layout_weight="1" />
                            <button id="refreshTemplatesBtn" text="刷新" />
                        </horizontal>
                        <text textSize="14sp" text="当前模板内容:" marginTop="8dp" />
                        <input id="selectedLlmPromptContent" lines="5" enabled="false" hint="此处显示选中模板的内容"/>

                        <text text="添加/编辑模板:" marginTop="16dp" />
                        <input id="newPromptTemplateName" hint="模板名称 (例如：通用评论模板)"/>
                        <input id="newPromptTemplateContent" hint="模板内容 (使用 {笔记内容} 代表笔记正文)" lines="4" />
                        <horizontal>
                            <button id="editSelectedTemplateBtn" text="编辑选中模板" layout_weight="1"/>
                            <button id="clearEditAreaBtn" text="清空编辑区域" layout_weight="1"/>
                        </horizontal>
                        <horizontal>
                            <button id="addLlmPromptTemplateBtn" text="保存模板" layout_weight="1"/>
                            <button id="deleteLlmPromptTemplateBtn" text="删除选中模板" layout_weight="1"/>
                        </horizontal>

                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="安全控制设置:" />
                        <text textSize="14sp" text="只评论评论数达到以下数量的笔记:" />
                        <input id="safetyMinCommentCount" hint="最少评论数 (0表示无限制)" inputType="number" text="0"/>

                        <text textSize="14sp" text="评论延迟时间 (秒):" marginTop="8dp" />
                        <horizontal>
                            <text text="最小:" textSize="12sp" layout_weight="0"/>
                            <input id="safetyCommentDelayMin" hint="5" inputType="number" text="5" layout_weight="1" marginLeft="4dp"/>
                            <text text="最大:" textSize="12sp" layout_weight="0" marginLeft="8dp"/>
                            <input id="safetyCommentDelayMax" hint="15" inputType="number" text="15" layout_weight="1" marginLeft="4dp"/>
                        </horizontal>

                        <text textSize="14sp" text="单个账号最多评论次数:" marginTop="8dp" />
                        <input id="safetyMaxCommentsPerAccount" hint="每日最大评论次数 (0表示无限制)" inputType="number" text="50"/>

                        <text textSize="16sp" textColor="#000000" marginTop="15dp" text="多账号管理:" />
                        <checkbox id="enableMultiAccount" text="启用多账号自动切换" />
                        <text textSize="14sp" text="账号列表 (格式: 用户名,密码 每行一组):" marginTop="8dp" />
                        <input id="accountList" hint="例如:&#10;***********,password123&#10;***********,password456" lines="4" />
                        <checkbox id="autoSwitchOnLimit" text="达到评论限制时自动切换账号" checked="true" />

                        <text textSize="16sp" textColor="#000000" marginTop="15dp" text="账号状态:" />
                        <text id="accountStatusDisplay" textSize="12sp" textColor="#666666" text="当前账号: 无 | 下个账号: 无" />
                        <horizontal marginTop="5dp">
                            <button id="refreshAccountStatusBtn" text="刷新账号状态" layout_weight="1" bg="#4CAF50" textColor="white"/>
                            <button id="switchAccountBtn" text="手动切换账号" layout_weight="1" bg="#FF9800" textColor="white"/>
                        </horizontal>

                        <text textSize="16sp" textColor="#000000" marginTop="15dp" text="安全状态:" />
                        <text id="safetyStatusDisplay" textSize="12sp" textColor="#666666" text="今日评论次数: 0 | 上次评论: 无" />
                        <horizontal marginTop="5dp">
                            <button id="refreshSafetyStatusBtn" text="刷新状态" layout_weight="1" bg="#4CAF50" textColor="white"/>
                            <button id="resetDailyCountBtn" text="重置计数" layout_weight="1" bg="#FF9800" textColor="white"/>
                        </horizontal>

                        <horizontal marginTop="10dp">
                            <button id="saveConfigBtn" text="保存配置" layout_weight="1" bg="#2196F3" textColor="white"/>
                            <button id="openAccessibilityBtn" text="开启无障碍服务" layout_weight="1"/>
                        </horizontal>
                    </vertical>

                    <!-- 用户列表选项卡内容 (初始隐藏) -->
                    <vertical id="tab_users_content" visibility="gone">
                        <text textSize="16sp" textColor="#000000" marginTop="10dp" text="用户信息管理:" />
                        
                        <!-- 统计信息 -->
                        <vertical bg="#f0f0f0" margin="8dp" padding="12dp">
                            <text textSize="14sp" textColor="#666666" text="统计信息:" />
                            <text id="userStatsText" textSize="12sp" text="总用户: 0 | 今日新增: 0" />
                        </vertical>
                        
                        <!-- 搜索和筛选 -->
                        <horizontal margin="8dp">
                            <input id="userSearchInput" hint="搜索用户..." layout_weight="1"/>
                            <button id="userSearchBtn" text="搜索" />
                        </horizontal>
                        
                        <horizontal margin="8dp">
                            <text text="地区筛选:" />
                            <spinner id="userRegionFilter" entries="全部|北京|上海|广东|浙江|江苏|山东|四川|湖北|湖南|河南|福建|安徽|河北|陕西|辽宁|云南|广西|江西|山西|吉林|贵州|重庆|天津|内蒙古|新疆|甘肃|海南|宁夏|青海|西藏|黑龙江|未知" layout_weight="1"/>
                            <button id="userFilterBtn" text="筛选" />
                        </horizontal>
                        
                        <!-- 操作按钮 -->
                        <horizontal margin="8dp">
                            <button id="exportCsvBtn" text="导出CSV" layout_weight="1" />
                            <button id="exportTxtBtn" text="导出TXT" layout_weight="1" />
                            <button id="updateRegionBtn" text="更新地区" layout_weight="1" />
                            <button id="clearUsersBtn" text="清空数据" layout_weight="1" bg="#f44336" textColor="white"/>
                        </horizontal>
                        
                        <!-- 用户列表 -->
                        <text textSize="14sp" text="用户列表:" margin="8dp"/>
                        <list id="usersList" layout_weight="1">
                            <vertical bg="#f8f8f8" margin="4dp" padding="8dp">
                                <text text="{{nickname}}" textSize="14sp" textColor="#000000"/>
                                <text text="小红书号: {{xhsId}}" textSize="12sp" textColor="#666666"/>
                                <text text="地区: {{region}} | 状态: {{status}}" textSize="12sp" textColor="#666666"/>
                                <text text="评论: {{commentText}}" textSize="12sp" maxLines="2" ellipsize="end"/>
                                <text text="采集时间: {{timeFormatted}}" textSize="10sp" textColor="#666666"/>
                            </vertical>
                        </list>
                    </vertical>
                </vertical>
            </ScrollView>
        </vertical>
    `;
}

/**
 * 选项卡切换逻辑
 * @param {Object} uiObject - UI对象
 * @param {string} activeTab - 激活的选项卡ID
 */
function switchTab(uiObject, activeTab) {
    console.log("UI_TABS: Switching to tab:", activeTab);

    // 隐藏所有选项卡内容
    if (uiObject.tab_main_content) uiObject.tab_main_content.setVisibility(8); // GONE
    if (uiObject.tab_settings_content) uiObject.tab_settings_content.setVisibility(8);
    if (uiObject.tab_users_content) uiObject.tab_users_content.setVisibility(8);

    // 重置所有选项卡按钮样式
    if (uiObject.tab_main) uiObject.tab_main.setTextColor(colors.parseColor("#666666"));
    if (uiObject.tab_settings) uiObject.tab_settings.setTextColor(colors.parseColor("#666666"));
    if (uiObject.tab_users) uiObject.tab_users.setTextColor(colors.parseColor("#666666"));

    // 显示选中的选项卡内容并设置按钮样式
    switch (activeTab) {
        case 'main':
            if (uiObject.tab_main_content) uiObject.tab_main_content.setVisibility(0); // VISIBLE
            if (uiObject.tab_main) uiObject.tab_main.setTextColor(colors.parseColor("#2196F3"));
            break;
        case 'settings':
            if (uiObject.tab_settings_content) uiObject.tab_settings_content.setVisibility(0);
            if (uiObject.tab_settings) uiObject.tab_settings.setTextColor(colors.parseColor("#2196F3"));
            break;
        case 'users':
            if (uiObject.tab_users_content) uiObject.tab_users_content.setVisibility(0);
            if (uiObject.tab_users) uiObject.tab_users.setTextColor(colors.parseColor("#2196F3"));
            break;
    }
}

/**
 * 附加选项卡事件处理器
 * @param {Object} uiObject - UI对象
 * @param {Object} dataManagerObject - 数据管理对象
 * @param {Object} utilsObject - 工具对象
 */
function attachTabEventHandlers(uiObject, dataManagerObject, utilsObject) {
    console.log("UI_TABS: Attaching tab event handlers.");

    // 选项卡切换事件
    if (uiObject.tab_main) {
        uiObject.tab_main.on("click", function () {
            switchTab(uiObject, 'main');
        });
    }

    if (uiObject.tab_settings) {
        uiObject.tab_settings.on("click", function () {
            switchTab(uiObject, 'settings');
        });
    }

    if (uiObject.tab_users) {
        uiObject.tab_users.on("click", function () {
            switchTab(uiObject, 'users');
            // 切换到用户列表时刷新数据
            refreshUsersList(uiObject, dataManagerObject, utilsObject);
        });
    }
}

/**
 * 刷新用户列表
 * @param {Object} uiObject - UI对象
 * @param {Object} dataManagerObject - 数据管理对象
 * @param {Object} utilsObject - 工具对象
 */
function refreshUsersList(uiObject, dataManagerObject, utilsObject) {
    try {
        if (!dataManagerObject || typeof dataManagerObject.getScrapedCommentUsers !== 'function') {
            utilsObject.log("UI_TABS: dataManagerObject not available for user list refresh");
            return;
        }

        var allUsers = dataManagerObject.getScrapedCommentUsers();
        var stats = dataManagerObject.getUserStatistics();

        // 更新统计信息
        if (uiObject.userStatsText) {
            var statsText = "总用户: " + stats.totalUsers + " | 今日新增: " + stats.recentUsers;
            uiObject.userStatsText.setText(statsText);
        }

        // 格式化用户数据用于列表显示
        var formattedUsers = [];
        for (var i = 0; i < allUsers.length; i++) {
            var user = allUsers[i];
            var commentText = user.commentText || user.comment || '';
            var formattedUser = {
                uid: user.uid,
                nickname: user.nickname,
                xhsId: user.xhsId || '未获取',
                commentText: commentText.substring(0, 50) + (commentText.length > 50 ? '...' : ''),
                noteId: user.noteId,
                noteTitle: user.noteTitle,
                region: user.region || '未知',
                status: user.status || 'collected',
                timestamp: user.timestamp,
                timeFormatted: user.timestamp ? new Date(user.timestamp).toLocaleString('zh-CN') : '未知'
            };
            formattedUsers.push(formattedUser);
        }

        // 更新用户列表
        if (uiObject.usersList) {
            uiObject.usersList.setDataSource(formattedUsers);
        }

        utilsObject.log("UI_TABS: User list refreshed with " + allUsers.length + " users");

    } catch (e) {
        utilsObject.log("UI_TABS: Error refreshing user list: " + e.toString());
    }
}

module.exports = {
    getTabLayoutXML: getTabLayoutXML,
    switchTab: switchTab,
    attachTabEventHandlers: attachTabEventHandlers,
    refreshUsersList: refreshUsersList
};
