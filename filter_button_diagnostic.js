/**
 * 筛选按钮诊断工具
 * 专门用于诊断和查找筛选按钮的问题
 */

var utils = require('./utils.js');

/**
 * 筛选按钮诊断主函数
 */
function diagnoseFilterButton() {
    utils.log("=== 筛选按钮诊断开始 ===");
    
    // 检查当前页面
    utils.log("1. 检查当前页面状态...");
    utils.log("设备信息: " + device.width + "x" + device.height);
    utils.log("当前包名: " + currentPackage());
    utils.log("当前活动: " + currentActivity());
    
    // 检查是否在搜索结果页
    var isSearchPage = checkSearchResultsPage();
    utils.log("是否在搜索结果页: " + isSearchPage);
    
    if (!isSearchPage) {
        utils.log("⚠️ 警告: 当前可能不在搜索结果页面，请先进入搜索结果页面");
    }
    
    // 尝试多种方法查找筛选按钮
    utils.log("\n2. 尝试查找筛选按钮...");
    
    var strategies = [
        {
            name: "方法1: 直接ID查找 (j16)",
            test: function() {
                return id("com.xingin.xhs:id/j16").findOne(2000);
            }
        },
        {
            name: "方法2: ID+Button类名",
            test: function() {
                return id("com.xingin.xhs:id/j16").className("android.widget.Button").findOne(2000);
            }
        },
        {
            name: "方法3: ID+可点击",
            test: function() {
                return id("com.xingin.xhs:id/j16").clickable(true).findOne(2000);
            }
        },
        {
            name: "方法4: 通过文字'筛选'查找",
            test: function() {
                return text("筛选").findOne(2000);
            }
        },
        {
            name: "方法5: 通过文字'筛选'+可点击",
            test: function() {
                return text("筛选").clickable(true).findOne(2000);
            }
        },
        {
            name: "方法6: 通过文字'筛选'+TextView类名",
            test: function() {
                return text("筛选").className("android.widget.TextView").findOne(2000);
            }
        },
        {
            name: "方法7: 通过描述'筛选'查找",
            test: function() {
                return desc("筛选").findOne(2000);
            }
        },
        {
            name: "方法8: 包含'filter'的ID",
            test: function() {
                return idContains("filter").findOne(2000);
            }
        }
    ];
    
    var foundElements = [];
    
    for (var i = 0; i < strategies.length; i++) {
        var strategy = strategies[i];
        utils.log("\n尝试 " + strategy.name + ":");
        
        try {
            var element = strategy.test();
            if (element) {
                utils.log("✅ 找到元素!");
                var elementInfo = {
                    strategy: strategy.name,
                    element: element,
                    id: element.id(),
                    text: element.text(),
                    desc: element.desc(),
                    className: element.className(),
                    bounds: element.bounds().toString(),
                    clickable: element.clickable(),
                    visible: element.visibleToUser(),
                    parent: element.parent() ? element.parent().className() : "无父元素"
                };
                
                utils.log("  ID: " + elementInfo.id);
                utils.log("  文字: '" + elementInfo.text + "'");
                utils.log("  描述: '" + elementInfo.desc + "'");
                utils.log("  类名: " + elementInfo.className);
                utils.log("  边界: " + elementInfo.bounds);
                utils.log("  可点击: " + elementInfo.clickable);
                utils.log("  可见: " + elementInfo.visible);
                utils.log("  父元素类名: " + elementInfo.parent);
                
                foundElements.push(elementInfo);
                
                // 如果找到TextView，尝试查找其父Button
                if (elementInfo.className === "android.widget.TextView" && elementInfo.text === "筛选") {
                    utils.log("  📋 这是TextView，尝试查找父Button...");
                    var parentElement = element.parent();
                    if (parentElement) {
                        utils.log("    父元素ID: " + parentElement.id());
                        utils.log("    父元素类名: " + parentElement.className());
                        utils.log("    父元素可点击: " + parentElement.clickable());
                        utils.log("    父元素可见: " + parentElement.visibleToUser());
                        
                        if (parentElement.className() === "android.widget.Button") {
                            utils.log("    ✅ 找到父Button! 这应该是真正的筛选按钮");
                        }
                    }
                }
                
            } else {
                utils.log("❌ 未找到元素");
            }
        } catch (e) {
            utils.log("❌ 查找出错: " + e.toString());
        }
    }
    
    // 总结结果
    utils.log("\n3. 诊断总结:");
    utils.log("找到 " + foundElements.length + " 个可能的筛选按钮元素");
    
    if (foundElements.length === 0) {
        utils.log("❌ 未找到任何筛选按钮元素");
        utils.log("建议:");
        utils.log("1. 确认当前在搜索结果页面");
        utils.log("2. 检查小红书版本是否有重大更新");
        utils.log("3. 使用Auto.js的布局分析工具手动查看页面结构");
    } else {
        utils.log("✅ 找到可能的筛选按钮:");
        for (var j = 0; j < foundElements.length; j++) {
            var elem = foundElements[j];
            utils.log("  " + (j + 1) + ". " + elem.strategy);
            utils.log("     ID: " + elem.id + ", 类名: " + elem.className);
            utils.log("     可点击: " + elem.clickable + ", 可见: " + elem.visible);
        }
        
        // 推荐最佳选择器
        var bestElement = findBestFilterButton(foundElements);
        if (bestElement) {
            utils.log("\n🎯 推荐使用的选择器:");
            utils.log("策略: " + bestElement.strategy);
            utils.log("选择器代码: " + generateSelectorCode(bestElement));
        }
    }
    
    utils.log("\n=== 筛选按钮诊断完成 ===");
    
    // 生成诊断报告
    generateDiagnosticReport(foundElements);
}

/**
 * 检查是否在搜索结果页面
 */
function checkSearchResultsPage() {
    // 检查多个可能的搜索结果页面指示器
    var indicators = [
        function() { return id("com.xingin.xhs:id/j16").exists(); },  // 新筛选按钮ID
        function() { return text("筛选").exists(); },                 // 筛选文字
        function() { return id("com.xingin.xhs:id/j15").exists(); },  // 笔记卡片容器
        function() { return className("androidx.recyclerview.widget.RecyclerView").exists(); } // 列表容器
    ];
    
    for (var i = 0; i < indicators.length; i++) {
        try {
            if (indicators[i]()) {
                return true;
            }
        } catch (e) {
            // 忽略错误，继续检查
        }
    }
    
    return false;
}

/**
 * 找到最佳的筛选按钮
 */
function findBestFilterButton(elements) {
    // 优先级：Button类型 > 可点击 > 可见 > 有正确ID
    var bestScore = -1;
    var bestElement = null;
    
    for (var i = 0; i < elements.length; i++) {
        var elem = elements[i];
        var score = 0;
        
        // Button类型得分最高
        if (elem.className === "android.widget.Button") score += 10;
        
        // 可点击很重要
        if (elem.clickable) score += 5;
        
        // 可见也很重要
        if (elem.visible) score += 3;
        
        // 有正确ID
        if (elem.id === "com.xingin.xhs:id/j16") score += 2;
        
        // 有正确文字
        if (elem.text === "筛选") score += 1;
        
        if (score > bestScore) {
            bestScore = score;
            bestElement = elem;
        }
    }
    
    return bestElement;
}

/**
 * 生成选择器代码
 */
function generateSelectorCode(element) {
    if (element.className === "android.widget.Button" && element.id) {
        return 'id("' + element.id + '").className("android.widget.Button").clickable(true).findOne(3000)';
    } else if (element.id) {
        return 'id("' + element.id + '").clickable(true).findOne(3000)';
    } else if (element.text) {
        return 'text("' + element.text + '").clickable(true).findOne(3000)';
    } else {
        return '// 无法生成选择器代码';
    }
}

/**
 * 生成诊断报告
 */
function generateDiagnosticReport(elements) {
    var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    var filename = "filter_button_diagnostic_" + timestamp + ".txt";
    
    var report = "=== 筛选按钮诊断报告 ===\n";
    report += "诊断时间: " + new Date().toISOString() + "\n";
    report += "设备信息: " + device.width + "x" + device.height + "\n";
    report += "当前包名: " + currentPackage() + "\n";
    report += "当前活动: " + currentActivity() + "\n\n";
    
    report += "找到的元素数量: " + elements.length + "\n\n";
    
    for (var i = 0; i < elements.length; i++) {
        var elem = elements[i];
        report += "元素 " + (i + 1) + ":\n";
        report += "  策略: " + elem.strategy + "\n";
        report += "  ID: " + elem.id + "\n";
        report += "  文字: '" + elem.text + "'\n";
        report += "  描述: '" + elem.desc + "'\n";
        report += "  类名: " + elem.className + "\n";
        report += "  边界: " + elem.bounds + "\n";
        report += "  可点击: " + elem.clickable + "\n";
        report += "  可见: " + elem.visible + "\n";
        report += "  父元素: " + elem.parent + "\n\n";
    }
    
    var bestElement = findBestFilterButton(elements);
    if (bestElement) {
        report += "推荐选择器:\n";
        report += generateSelectorCode(bestElement) + "\n\n";
    }
    
    report += "建议:\n";
    if (elements.length === 0) {
        report += "1. 确认当前在搜索结果页面\n";
        report += "2. 检查小红书版本是否有重大更新\n";
        report += "3. 使用Auto.js布局分析工具手动查看\n";
    } else {
        report += "1. 使用推荐的选择器更新代码\n";
        report += "2. 测试选择器的稳定性\n";
        report += "3. 如有问题，尝试其他找到的选择器\n";
    }
    
    try {
        files.write(filename, report);
        utils.log("📄 诊断报告已保存: " + filename);
    } catch (e) {
        utils.log("保存诊断报告失败: " + e.toString());
    }
}

/**
 * 主函数
 */
function main() {
    var confirm = dialogs.confirm("筛选按钮诊断工具", 
        "此工具将诊断筛选按钮查找问题。\n\n" +
        "请确保:\n" +
        "1. 已打开小红书app\n" +
        "2. 当前在搜索结果页面\n" +
        "3. 可以看到筛选按钮\n\n" +
        "是否开始诊断？"
    );
    
    if (confirm) {
        diagnoseFilterButton();
        dialogs.alert("诊断完成", "请查看控制台日志和生成的诊断报告文件。");
    }
}

// 如果直接运行此文件，执行主函数
if (typeof module === 'undefined') {
    main();
}

module.exports = {
    diagnoseFilterButton
};
