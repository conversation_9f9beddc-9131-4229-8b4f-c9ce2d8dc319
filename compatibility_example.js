/**
 * 设备兼容性使用示例
 * 展示如何在现有代码中使用多设备兼容的元素查找
 */

const utils = require('./utils.js');
const { smartFinder } = require('./device_compatibility.js');

/**
 * 兼容性搜索功能示例
 */
function compatibleSearch(keyword) {
    utils.log("COMPAT_EXAMPLE: 开始兼容性搜索...");
    
    try {
        // 使用智能查找器查找搜索框
        const searchBox = smartFinder.findElement('SEARCH_BOX');
        if (!searchBox) {
            utils.log("COMPAT_EXAMPLE: 未找到搜索框");
            return false;
        }
        
        // 输入关键词
        searchBox.setText(keyword);
        sleep(1000);
        
        // 使用智能查找器查找搜索按钮
        const searchButton = smartFinder.findElement('SEARCH_BUTTON');
        if (!searchButton) {
            utils.log("COMPAT_EXAMPLE: 未找到搜索按钮");
            return false;
        }
        
        // 点击搜索
        if (searchButton.click()) {
            utils.log("COMPAT_EXAMPLE: 搜索成功");
            return true;
        } else {
            utils.log("COMPAT_EXAMPLE: 搜索按钮点击失败");
            return false;
        }
        
    } catch (e) {
        utils.log("COMPAT_EXAMPLE: 搜索过程出错: " + e.toString());
        return false;
    }
}

/**
 * 兼容性笔记信息提取示例
 */
function compatibleExtractNoteInfo() {
    utils.log("COMPAT_EXAMPLE: 开始兼容性笔记信息提取...");
    
    try {
        // 批量查找笔记相关元素
        const elements = smartFinder.findElements([
            'NOTE_CARD_TITLE',
            'NOTE_CARD_AUTHOR'
        ]);
        
        const result = {
            title: null,
            author: null
        };
        
        // 提取标题
        if (elements.NOTE_CARD_TITLE) {
            result.title = elements.NOTE_CARD_TITLE.text();
            utils.log("COMPAT_EXAMPLE: 提取到标题: " + result.title);
        }
        
        // 提取作者
        if (elements.NOTE_CARD_AUTHOR) {
            result.author = elements.NOTE_CARD_AUTHOR.text();
            utils.log("COMPAT_EXAMPLE: 提取到作者: " + result.author);
        }
        
        return result;
        
    } catch (e) {
        utils.log("COMPAT_EXAMPLE: 笔记信息提取出错: " + e.toString());
        return null;
    }
}

/**
 * 兼容性图文笔记详情页处理示例
 */
function compatibleProcessImageNote() {
    utils.log("COMPAT_EXAMPLE: 开始兼容性图文笔记处理...");
    
    try {
        // 批量查找图文笔记相关元素
        const elements = smartFinder.findElements([
            'IMAGE_NOTE_AUTHOR_NICKNAME',
            'IMAGE_NOTE_TITLE',
            'IMAGE_NOTE_CONTENT',
            'IMAGE_NOTE_COMMENT_BUTTON',
            'IMAGE_NOTE_TYPE_INDICATOR'
        ]);
        
        const noteInfo = {
            author: null,
            title: null,
            content: null,
            isImageNote: false
        };
        
        // 确认是图文笔记
        if (elements.IMAGE_NOTE_TYPE_INDICATOR) {
            noteInfo.isImageNote = true;
            utils.log("COMPAT_EXAMPLE: 确认为图文笔记");
        }
        
        // 提取作者昵称
        if (elements.IMAGE_NOTE_AUTHOR_NICKNAME) {
            noteInfo.author = elements.IMAGE_NOTE_AUTHOR_NICKNAME.text();
            utils.log("COMPAT_EXAMPLE: 作者: " + noteInfo.author);
        }
        
        // 提取笔记标题
        if (elements.IMAGE_NOTE_TITLE) {
            noteInfo.title = elements.IMAGE_NOTE_TITLE.text();
            utils.log("COMPAT_EXAMPLE: 标题: " + noteInfo.title);
        }
        
        // 提取笔记内容
        if (elements.IMAGE_NOTE_CONTENT) {
            noteInfo.content = elements.IMAGE_NOTE_CONTENT.text();
            utils.log("COMPAT_EXAMPLE: 内容: " + noteInfo.content.substring(0, 50) + "...");
        }
        
        // 点击评论按钮
        if (elements.IMAGE_NOTE_COMMENT_BUTTON) {
            if (elements.IMAGE_NOTE_COMMENT_BUTTON.click()) {
                utils.log("COMPAT_EXAMPLE: 评论按钮点击成功");
            }
        }
        
        return noteInfo;
        
    } catch (e) {
        utils.log("COMPAT_EXAMPLE: 图文笔记处理出错: " + e.toString());
        return null;
    }
}

/**
 * 兼容性视频笔记详情页处理示例
 */
function compatibleProcessVideoNote() {
    utils.log("COMPAT_EXAMPLE: 开始兼容性视频笔记处理...");
    
    try {
        // 批量查找视频笔记相关元素
        const elements = smartFinder.findElements([
            'VIDEO_NOTE_AUTHOR_NICKNAME',
            'VIDEO_NOTE_WM_CONTAINER',
            'VIDEO_NOTE_C9Y_CONTAINER'
        ]);
        
        const noteInfo = {
            author: null,
            isVideoNote: false
        };
        
        // 确认是视频笔记
        if (elements.VIDEO_NOTE_WM_CONTAINER && elements.VIDEO_NOTE_C9Y_CONTAINER) {
            noteInfo.isVideoNote = true;
            utils.log("COMPAT_EXAMPLE: 确认为视频笔记");
        }
        
        // 提取作者昵称
        if (elements.VIDEO_NOTE_AUTHOR_NICKNAME) {
            noteInfo.author = elements.VIDEO_NOTE_AUTHOR_NICKNAME.text();
            utils.log("COMPAT_EXAMPLE: 视频作者: " + noteInfo.author);
        }
        
        return noteInfo;
        
    } catch (e) {
        utils.log("COMPAT_EXAMPLE: 视频笔记处理出错: " + e.toString());
        return null;
    }
}

/**
 * 兼容性评论系统处理示例
 */
function compatibleProcessComments() {
    utils.log("COMPAT_EXAMPLE: 开始兼容性评论处理...");
    
    try {
        // 批量查找评论相关元素
        const elements = smartFinder.findElements([
            'COMMENT_AUTHOR',
            'COMMENT_CONTENT',
            'COMMENT_COUNT_INDICATOR',
            'COMMENT_END_INDICATOR'
        ]);
        
        const comments = [];
        
        // 检查评论数量
        if (elements.COMMENT_COUNT_INDICATOR) {
            const countText = elements.COMMENT_COUNT_INDICATOR.text();
            utils.log("COMPAT_EXAMPLE: 评论数量指示: " + countText);
        }
        
        // 查找所有评论
        if (elements.COMMENT_AUTHOR && elements.COMMENT_CONTENT) {
            const authors = smartFinder.findElement('COMMENT_AUTHOR').parent().parent().find(smartFinder.findElement('COMMENT_AUTHOR'));
            const contents = smartFinder.findElement('COMMENT_CONTENT').parent().parent().find(smartFinder.findElement('COMMENT_CONTENT'));
            
            for (let i = 0; i < Math.min(authors.length, contents.length); i++) {
                comments.push({
                    author: authors[i].text(),
                    content: contents[i].text()
                });
            }
            
            utils.log("COMPAT_EXAMPLE: 提取到 " + comments.length + " 条评论");
        }
        
        // 检查是否到底
        if (elements.COMMENT_END_INDICATOR) {
            utils.log("COMPAT_EXAMPLE: 已到评论底部");
        }
        
        return comments;
        
    } catch (e) {
        utils.log("COMPAT_EXAMPLE: 评论处理出错: " + e.toString());
        return [];
    }
}

/**
 * 显示缓存统计
 */
function showCacheStats() {
    const stats = smartFinder.getCacheStats();
    
    utils.log("=== 智能查找器缓存统计 ===");
    utils.log("设备指纹: " + stats.deviceFingerprint);
    utils.log("缓存元素数量: " + stats.cachedElements);
    
    if (stats.cachedElements > 0) {
        utils.log("缓存详情:");
        for (const [elementKey, strategyName] of Object.entries(stats.cacheDetails)) {
            utils.log("  " + elementKey + ": " + strategyName);
        }
    }
}

/**
 * 测试兼容性功能
 */
function testCompatibility() {
    utils.log("=== 开始兼容性测试 ===");
    
    // 测试搜索功能
    utils.log("1. 测试搜索功能");
    compatibleSearch("测试关键词");
    
    // 测试笔记信息提取
    utils.log("2. 测试笔记信息提取");
    compatibleExtractNoteInfo();
    
    // 测试图文笔记处理
    utils.log("3. 测试图文笔记处理");
    compatibleProcessImageNote();
    
    // 测试视频笔记处理
    utils.log("4. 测试视频笔记处理");
    compatibleProcessVideoNote();
    
    // 测试评论系统
    utils.log("5. 测试评论系统");
    compatibleProcessComments();
    
    // 显示缓存统计
    utils.log("6. 显示缓存统计");
    showCacheStats();
    
    utils.log("=== 兼容性测试完成 ===");
}

// 如果直接运行此文件，执行测试
if (typeof module === 'undefined') {
    testCompatibility();
}

module.exports = {
    compatibleSearch,
    compatibleExtractNoteInfo,
    compatibleProcessImageNote,
    compatibleProcessVideoNote,
    compatibleProcessComments,
    showCacheStats,
    testCompatibility
};
