/**
 * 小红书适配工具启动脚本
 * 使用方法：在Auto.js中运行此脚本
 */

const QuickAdapter = require('./quick_adapter.js');
const utils = require('./utils.js');

// 显示适配工具界面
function showAdapterUI() {
    const window = floaty.window(
        <vertical padding="16" bg="#ffffff">
            <text text="小红书适配工具" textSize="18" textColor="#333333" gravity="center"/>
            <text text="请选择要适配的页面类型" textSize="14" textColor="#666666" gravity="center" margin="0 0 16 0"/>
            
            <button id="adaptSearch" text="适配搜索页面" textSize="16" bg="#4CAF50" textColor="#ffffff" margin="0 0 8 0"/>
            <button id="adaptNoteDetail" text="适配笔记详情页" textSize="16" bg="#2196F3" textColor="#ffffff" margin="0 0 8 0"/>
            <button id="adaptFull" text="完整适配" textSize="16" bg="#FF9800" textColor="#ffffff" margin="0 0 8 0"/>
            <button id="diagnose" text="页面诊断" textSize="16" bg="#9C27B0" textColor="#ffffff" margin="0 0 8 0"/>
            <button id="close" text="关闭" textSize="16" bg="#F44336" textColor="#ffffff"/>
        </vertical>
    );

    window.setPosition(100, 200);
    window.setSize(300, 400);

    // 按钮事件处理
    window.adaptSearch.click(() => {
        window.close();
        runSearchPageAdapter();
    });

    window.adaptNoteDetail.click(() => {
        window.close();
        runNoteDetailAdapter();
    });

    window.adaptFull.click(() => {
        window.close();
        runFullAdapter();
    });

    window.diagnose.click(() => {
        window.close();
        runPageDiagnosis();
    });

    window.close.click(() => {
        window.close();
    });
}

/**
 * 运行搜索页面适配
 */
function runSearchPageAdapter() {
    utils.log("=== 开始搜索页面适配 ===");
    
    try {
        const adapter = new QuickAdapter();
        
        // 检查是否在搜索页面
        if (!adapter.detectCurrentPage() === 'search') {
            dialogs.alert("提示", "请先进入小红书搜索页面，然后重新运行适配工具");
            return;
        }
        
        // 执行搜索页面适配
        adapter.adaptSearchPage().then(() => {
            dialogs.alert("完成", "搜索页面适配完成！请查看日志和生成的配置文件。");
        });
        
    } catch (e) {
        utils.log("搜索页面适配失败: " + e.toString());
        dialogs.alert("错误", "适配失败: " + e.toString());
    }
}

/**
 * 运行笔记详情页适配
 */
function runNoteDetailAdapter() {
    utils.log("=== 开始笔记详情页适配 ===");
    
    try {
        const adapter = new QuickAdapter();
        
        // 检查是否在笔记详情页
        if (!adapter.detectCurrentPage() === 'note_detail') {
            dialogs.alert("提示", "请先进入小红书笔记详情页，然后重新运行适配工具");
            return;
        }
        
        // 执行笔记详情页适配
        adapter.adaptNoteDetailPage().then(() => {
            dialogs.alert("完成", "笔记详情页适配完成！请查看日志和生成的配置文件。");
        });
        
    } catch (e) {
        utils.log("笔记详情页适配失败: " + e.toString());
        dialogs.alert("错误", "适配失败: " + e.toString());
    }
}

/**
 * 运行完整适配
 */
function runFullAdapter() {
    utils.log("=== 开始完整适配 ===");
    
    const confirmResult = dialogs.confirm("确认", 
        "完整适配将会：\n" +
        "1. 分析当前页面\n" +
        "2. 发现所有关键元素\n" +
        "3. 生成新的配置文件\n" +
        "4. 提供适配建议\n\n" +
        "这个过程可能需要几分钟，是否继续？"
    );
    
    if (!confirmResult) {
        return;
    }
    
    try {
        const adapter = new QuickAdapter();
        
        // 显示进度提示
        const progressDialog = dialogs.build({
            title: "适配进行中",
            content: "正在分析页面元素，请稍候...",
            cancelable: false
        }).show();
        
        // 执行完整适配
        adapter.runFullAdaptation().then(() => {
            progressDialog.dismiss();
            dialogs.alert("完成", 
                "完整适配完成！\n\n" +
                "请查看：\n" +
                "1. 控制台日志\n" +
                "2. 生成的配置文件\n" +
                "3. 适配建议\n\n" +
                "根据建议更新相应的代码文件。"
            );
        }).catch((e) => {
            progressDialog.dismiss();
            utils.log("完整适配失败: " + e.toString());
            dialogs.alert("错误", "适配失败: " + e.toString());
        });
        
    } catch (e) {
        utils.log("完整适配失败: " + e.toString());
        dialogs.alert("错误", "适配失败: " + e.toString());
    }
}

/**
 * 运行页面诊断
 */
function runPageDiagnosis() {
    utils.log("=== 开始页面诊断 ===");
    
    try {
        // 基本页面信息
        utils.log("页面基本信息:");
        utils.log("- 当前包名: " + currentPackage());
        utils.log("- 当前活动: " + currentActivity());
        utils.log("- 屏幕尺寸: " + device.width + "x" + device.height);
        
        // 可点击元素统计
        const clickableElements = clickable(true).visibleToUser(true).find();
        utils.log("- 可点击元素数量: " + clickableElements.length);
        
        // 输入框统计
        const editTexts = className("android.widget.EditText").find();
        utils.log("- 输入框数量: " + editTexts.length);
        
        // 列表控件统计
        const recyclerViews = className("androidx.recyclerview.widget.RecyclerView").find();
        utils.log("- RecyclerView数量: " + recyclerViews.length);
        
        // 详细元素信息
        utils.log("\n前10个可点击元素详情:");
        for (let i = 0; i < Math.min(clickableElements.length, 10); i++) {
            const elem = clickableElements[i];
            utils.log(`[${i}] ID:${elem.id()} Text:"${elem.text()}" Desc:"${elem.desc()}" Class:${elem.className()}`);
        }
        
        // 搜索相关元素
        const searchElements = textContains("搜索").find();
        utils.log("\n包含'搜索'的元素:");
        for (let i = 0; i < searchElements.length; i++) {
            const elem = searchElements[i];
            utils.log(`[${i}] ID:${elem.id()} Text:"${elem.text()}" Desc:"${elem.desc()}" Clickable:${elem.clickable()}`);
        }
        
        dialogs.alert("诊断完成", "页面诊断完成！请查看控制台日志获取详细信息。");
        
    } catch (e) {
        utils.log("页面诊断失败: " + e.toString());
        dialogs.alert("错误", "诊断失败: " + e.toString());
    }
}

// 主函数
function main() {
    // 检查是否在小红书app中
    if (currentPackage() !== "com.xingin.xhs") {
        dialogs.alert("提示", "请先打开小红书app，然后重新运行此脚本");
        return;
    }
    
    // 显示适配工具界面
    showAdapterUI();
}

// 启动适配工具
main();
