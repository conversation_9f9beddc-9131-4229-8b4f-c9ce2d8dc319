/**
 * 小红书快速适配工具
 * 用于快速适配新版本的小红书app
 */

const utils = require('./utils.js');
const ElementDiscovery = require('./element_discovery.js');

/**
 * 快速适配器
 */
class QuickAdapter {
    constructor() {
        this.discovery = new ElementDiscovery();
        this.newSelectors = {};
    }

    /**
     * 执行完整的适配流程
     */
    async runFullAdaptation() {
        utils.log("=== 开始小红书快速适配 ===");
        
        try {
            // 第一步：检查当前页面
            const currentPage = this.detectCurrentPage();
            utils.log(`当前页面类型: ${currentPage}`);
            
            // 第二步：根据页面类型进行适配
            if (currentPage === 'search') {
                await this.adaptSearchPage();
            } else if (currentPage === 'note_detail') {
                await this.adaptNoteDetailPage();
            } else if (currentPage === 'home') {
                await this.adaptHomePage();
            } else {
                utils.log("未识别的页面类型，执行通用适配...");
                await this.adaptGenericPage();
            }
            
            // 第三步：生成新的配置文件
            this.generateNewConfig();
            
            // 第四步：提供适配建议
            this.provideAdaptationSuggestions();
            
        } catch (e) {
            utils.log("适配过程中出错: " + e.toString());
        }
        
        utils.log("=== 小红书快速适配完成 ===");
    }

    /**
     * 检测当前页面类型
     */
    detectCurrentPage() {
        // 检查是否在搜索页面
        if (className("android.widget.EditText").textContains("搜索").exists() ||
            className("android.widget.EditText").hintContains("搜索").exists()) {
            return 'search';
        }
        
        // 检查是否在笔记详情页
        if (textContains("评论").exists() && 
            (descContains("点赞").exists() || descContains("分享").exists())) {
            return 'note_detail';
        }
        
        // 检查是否在首页
        if (textContains("发现").exists() || textContains("关注").exists()) {
            return 'home';
        }
        
        return 'unknown';
    }

    /**
     * 适配搜索页面
     */
    async adaptSearchPage() {
        utils.log("正在适配搜索页面...");
        
        const elements = this.discovery.discoverSearchPageElements();
        this.newSelectors.search = elements;
        
        // 测试搜索功能
        if (elements.searchBox && elements.searchButton) {
            utils.log("尝试测试搜索功能...");
            try {
                // 输入测试关键词
                elements.searchBox.element.setText("测试");
                sleep(1000);
                
                // 点击搜索按钮
                if (elements.searchButton.element.click()) {
                    utils.log("✅ 搜索功能测试成功");
                    sleep(3000);
                    
                    // 返回搜索页面
                    if (elements.backButton && elements.backButton.element) {
                        elements.backButton.element.click();
                        sleep(2000);
                    } else {
                        back();
                        sleep(2000);
                    }
                } else {
                    utils.log("❌ 搜索按钮点击失败");
                }
            } catch (e) {
                utils.log("搜索功能测试失败: " + e.toString());
            }
        }
    }

    /**
     * 适配笔记详情页面
     */
    async adaptNoteDetailPage() {
        utils.log("正在适配笔记详情页面...");
        
        const elements = this.discovery.discoverNoteDetailElements();
        this.newSelectors.noteDetail = elements;
        
        // 测试评论功能
        if (elements.commentButton) {
            utils.log("尝试测试评论功能...");
            try {
                if (elements.commentButton.element.click()) {
                    utils.log("✅ 评论按钮点击成功");
                    sleep(2000);
                    
                    // 如果有评论输入框，测试输入
                    if (elements.commentInput && elements.commentInput.element) {
                        elements.commentInput.element.setText("测试评论");
                        sleep(1000);
                        
                        // 清空测试内容
                        elements.commentInput.element.setText("");
                        utils.log("✅ 评论输入测试成功");
                    }
                } else {
                    utils.log("❌ 评论按钮点击失败");
                }
            } catch (e) {
                utils.log("评论功能测试失败: " + e.toString());
            }
        }
    }

    /**
     * 适配首页
     */
    async adaptHomePage() {
        utils.log("正在适配首页...");
        
        // 发现首页关键元素
        const elements = {
            searchEntry: this.findSearchEntry(),
            noteList: this.findNoteList(),
            tabBar: this.findTabBar()
        };
        
        this.newSelectors.home = elements;
    }

    /**
     * 通用页面适配
     */
    async adaptGenericPage() {
        utils.log("正在执行通用页面适配...");
        
        // 列出所有可点击元素
        const clickableElements = clickable(true).visibleToUser(true).find();
        utils.log(`发现 ${clickableElements.length} 个可点击元素`);
        
        // 分析元素特征
        const elementAnalysis = this.analyzeElements(clickableElements);
        this.newSelectors.generic = elementAnalysis;
    }

    /**
     * 查找搜索入口
     */
    findSearchEntry() {
        const strategies = [
            () => textContains("搜索").clickable(true).findOne(2000),
            () => descContains("搜索").clickable(true).findOne(2000),
            () => idContains("search").clickable(true).findOne(2000)
        ];
        
        return this.discovery.tryStrategies("搜索入口", strategies);
    }

    /**
     * 查找笔记列表
     */
    findNoteList() {
        const strategies = [
            () => className("androidx.recyclerview.widget.RecyclerView").findOne(2000),
            () => className("android.widget.ListView").findOne(2000),
            () => idContains("list").findOne(2000),
            () => idContains("recycler").findOne(2000)
        ];
        
        return this.discovery.tryStrategies("笔记列表", strategies);
    }

    /**
     * 查找底部标签栏
     */
    findTabBar() {
        const strategies = [
            () => className("android.widget.TabHost").findOne(2000),
            () => idContains("tab").findOne(2000),
            () => idContains("bottom").findOne(2000)
        ];
        
        return this.discovery.tryStrategies("底部标签栏", strategies);
    }

    /**
     * 分析元素特征
     */
    analyzeElements(elements) {
        const analysis = {
            total: elements.length,
            buttons: [],
            inputs: [],
            lists: [],
            others: []
        };
        
        for (let i = 0; i < Math.min(elements.length, 20); i++) {
            const elem = elements[i];
            const info = this.discovery.getElementInfo(elem);
            
            if (elem.className().includes("Button")) {
                analysis.buttons.push(info);
            } else if (elem.className().includes("EditText")) {
                analysis.inputs.push(info);
            } else if (elem.className().includes("RecyclerView") || elem.className().includes("ListView")) {
                analysis.lists.push(info);
            } else {
                analysis.others.push(info);
            }
        }
        
        return analysis;
    }

    /**
     * 生成新的配置文件
     */
    generateNewConfig() {
        utils.log("正在生成新的配置文件...");
        
        const config = {
            version: "auto_generated_" + new Date().toISOString(),
            timestamp: new Date().toISOString(),
            deviceInfo: {
                width: device.width,
                height: device.height,
                package: currentPackage(),
                activity: currentActivity()
            },
            selectors: {}
        };
        
        // 转换发现的元素为配置格式
        for (const [pageName, pageElements] of Object.entries(this.newSelectors)) {
            config.selectors[pageName] = {};
            
            if (typeof pageElements === 'object' && pageElements !== null) {
                for (const [elementName, elementData] of Object.entries(pageElements)) {
                    if (elementData && elementData.element) {
                        config.selectors[pageName][elementName] = {
                            id: elementData.element.id(),
                            text: elementData.element.text(),
                            desc: elementData.element.desc(),
                            className: elementData.element.className()
                        };
                    }
                }
            }
        }
        
        // 保存配置文件
        const filename = `new_selectors_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        try {
            files.write(filename, JSON.stringify(config, null, 2));
            utils.log(`✅ 新配置文件已保存: ${filename}`);
        } catch (e) {
            utils.log(`保存配置文件失败: ${e.toString()}`);
        }
    }

    /**
     * 提供适配建议
     */
    provideAdaptationSuggestions() {
        utils.log("=== 适配建议 ===");
        
        const suggestions = [];
        
        // 检查搜索功能
        if (this.newSelectors.search) {
            if (this.newSelectors.search.searchBox && this.newSelectors.search.searchButton) {
                suggestions.push("✅ 搜索功能元素已找到，可以更新xhs_actions.js中的搜索相关选择器");
            } else {
                suggestions.push("❌ 搜索功能元素缺失，需要手动检查搜索页面");
            }
        }
        
        // 检查笔记详情功能
        if (this.newSelectors.noteDetail) {
            if (this.newSelectors.noteDetail.commentButton) {
                suggestions.push("✅ 评论功能元素已找到，可以更新笔记详情页相关选择器");
            } else {
                suggestions.push("❌ 评论功能元素缺失，需要手动检查笔记详情页");
            }
        }
        
        // 输出建议
        suggestions.forEach(suggestion => utils.log(suggestion));
        
        utils.log("建议：");
        utils.log("1. 查看生成的配置文件，确认元素ID");
        utils.log("2. 更新相应的选择器配置");
        utils.log("3. 逐个测试各项功能");
        utils.log("4. 如有问题，可重新运行适配工具");
    }
}

// 导出适配器
module.exports = QuickAdapter;

// 如果直接运行此文件，执行适配
if (typeof module === 'undefined') {
    const adapter = new QuickAdapter();
    adapter.runFullAdaptation();
}
