// LLM服务模块：负责调用大模型API生成评论
const utils = require(files.path("./utils.js"));
const doubaoWebView = require(files.path("./doubao_webview.js"));

/**
 * 调用LLM API生成评论。
 * @param {string|null} noteContent - 笔记内容，如果为null或空则无法调用。
 * @param {string} apiUrl - LLM API的URL地址。
 * @param {string} modelName - LLM模型的名称。
 * @param {string} userPrompt - 用户定义的系统提示词 (System Prompt)。
 * @param {number|string} temperature - LLM的温度参数。
 * @param {number|string} maxTokens - LLM的最大Token数。
 * @param {boolean} useDoubaoProxy - 是否使用豆包AI。
 * @param {string} doubaoPhoneNumber - 豆包登录手机号。
 * @param {boolean} preferManualCode - 是否优先使用手动输入验证码。
 * @returns {{success: boolean, comment: string|null, error: string|null}}
 *          返回一个包含操作结果的对象。
 *          - success: 布尔值，表示操作是否成功。
 *          - comment: 字符串或null，成功时为生成的评论文本，失败时为null。
 *          - error: 字符串或null，失败时的错误信息，成功时为null。
 */
function generateCommentWithLLM(noteContent, apiUrl, modelName, userPrompt, temperature, maxTokens, useDoubaoProxy, doubaoPhoneNumber, preferManualCode) {
    // 检查笔记内容
    if (!noteContent || noteContent.trim() === "") {
        utils.log("LLM_SERVICE: 笔记内容为空，无法调用LLM", "error");
        return { success: false, comment: null, error: "笔记内容为空，无法调用LLM" };
    }

    // 对于豆包WebView模式，不需要检查API URL
    if (!useDoubaoProxy && !apiUrl) {
        utils.log("LLM_SERVICE: 通用LLM模式下API URL为空，无法调用LLM", "error");
        return { success: false, comment: null, error: "通用LLM模式下API URL为空，无法调用LLM" };
    }

    const tempValue = parseFloat(temperature);
    const maxTokensValue = parseInt(maxTokens);

    if (isNaN(tempValue)) {
        utils.log(`LLM_SERVICE: Temperature值无效: ${temperature}，将使用默认值0.7`, "warn");
        temperature = 0.7;
    } else {
        temperature = tempValue;
    }

    if (isNaN(maxTokensValue) || maxTokensValue <= 0) {
        utils.log(`LLM_SERVICE: Max Tokens值无效: ${maxTokens}，将使用默认值512`, "warn");
        maxTokens = 512;
    } else {
        maxTokens = maxTokensValue;
    }


    // 根据是否使用豆包反代来构建不同的请求
    var payload, headers;

    if (useDoubaoProxy) {
        // 豆包WebView模式 - 直接使用网页版豆包
        utils.log("LLM_SERVICE: 使用豆包AI模式");

        try {
            var webViewResult = doubaoWebView.generateCommentWithDoubaoWebView(noteContent, userPrompt, doubaoPhoneNumber, preferManualCode);
            if (webViewResult.success && webViewResult.comment) {
                utils.log("LLM_SERVICE: 豆包AI生成评论成功: " + webViewResult.comment.substring(0, 50) + "...");
                // 重要：传递WebView实例给调用方，用于后续关闭
                return {
                    success: true,
                    comment: webViewResult.comment,
                    error: null,
                    webview: webViewResult.webview // 传递WebView实例
                };
            } else {
                utils.log("LLM_SERVICE: 豆包AI生成失败: " + (webViewResult.error || "未知错误"), "error");
                return { success: false, comment: null, error: webViewResult.error || "豆包AI生成失败" };
            }
        } catch (webViewError) {
            utils.log("LLM_SERVICE: 豆包AI调用异常: " + webViewError, "error");
            return { success: false, comment: null, error: "豆包AI调用异常: " + webViewError };
        }
    } else {
        // 通用LLM模式
        payload = {
            model: modelName,
            messages: [
                { role: "system", content: userPrompt },
                { role: "user", content: noteContent }
            ],
            temperature: temperature,
            max_tokens: maxTokens
        };

        headers = {
            "Content-Type": "application/json"
            // 如果API需要Authorization Token，可以在这里添加
            // "Authorization": "Bearer YOUR_API_KEY"
        };

        utils.log(`LLM_SERVICE: 调用通用LLM API: ${apiUrl} 模型: ${modelName}`);
    }

    utils.log(`LLM_SERVICE: Payload: ${JSON.stringify(payload, null, 2)}`);

    try {
        // @ts-ignore
        const response = http.request(apiUrl, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(payload),
            timeout: 30000 // 30秒超时
        });

        const statusCode = response.statusCode;
        const responseBodyString = response.body.string();
        utils.log(`LLM_SERVICE: API响应状态码: ${statusCode}`);

        if (statusCode < 200 || statusCode >= 300) {
            utils.log(`LLM_SERVICE: LLM API请求失败。状态码: ${statusCode}, 响应体: ${responseBodyString}`, "error");
            return { success: false, comment: null, error: `LLM API请求失败，状态码: ${statusCode}` };
        }

        utils.log(`LLM_SERVICE: API原始响应体: ${responseBodyString}`);
        const responseJson = JSON.parse(responseBodyString);

        if (responseJson.choices && responseJson.choices.length > 0 && responseJson.choices[0].message) {
            const messageContent = responseJson.choices[0].message.content;
            utils.log(`LLM_SERVICE: 检查返回的content: "${messageContent}" (类型: ${typeof messageContent}, 长度: ${messageContent ? messageContent.length : 'null'})`);

            if (messageContent && messageContent.trim() !== "") {
                const generatedCommentText = messageContent.trim();
                utils.log(`LLM_SERVICE: LLM成功生成评论: "${generatedCommentText}"`);
                return { success: true, comment: generatedCommentText, error: null };
            } else {
                utils.log("LLM_SERVICE: LLM返回了空的评论内容 - content字段存在但为空", "warn");
                utils.log(`LLM_SERVICE: 完整响应分析: choices[0].message = ${JSON.stringify(responseJson.choices[0].message)}`);
                return { success: false, comment: null, error: "LLM返回了空的评论内容" };
            }
        } else {
            utils.log(`LLM_SERVICE: LLM API响应格式不符合预期。响应: ${JSON.stringify(responseJson)}`, "error");
            if (!responseJson.choices) {
                utils.log("LLM_SERVICE: 缺少choices字段");
            } else if (responseJson.choices.length === 0) {
                utils.log("LLM_SERVICE: choices数组为空");
            } else if (!responseJson.choices[0].message) {
                utils.log("LLM_SERVICE: 缺少message字段");
            }
            return { success: false, comment: null, error: "LLM API响应格式不符合预期" };
        }
    } catch (e) {
        // @ts-ignore
        utils.log(`LLM_SERVICE: 调用LLM API时发生异常: ${e.toString()}`, "error");
        // @ts-ignore
        return { success: false, comment: null, error: e.toString() };
    }
}

module.exports = {
    generateCommentWithLLM: generateCommentWithLLM
};

utils.log("LLM服务模块加载完毕 (llm_service.js)");