/**
 * 小红书元素发现和适配工具
 * 用于快速发现新版本小红书的元素ID和结构
 */

const utils = require('./utils.js');

/**
 * 页面元素发现器
 */
class ElementDiscovery {
    constructor() {
        this.discoveredElements = {};
    }

    /**
     * 发现搜索页面的所有关键元素
     */
    discoverSearchPageElements() {
        utils.log("=== 开始发现搜索页面元素 ===");
        
        const elements = {
            searchBox: null,
            searchButton: null,
            filterButton: null,
            backButton: null
        };

        try {
            // 1. 发现搜索框
            elements.searchBox = this.discoverSearchBox();
            
            // 2. 发现搜索按钮
            elements.searchButton = this.discoverSearchButton();
            
            // 3. 发现筛选按钮
            elements.filterButton = this.discoverFilterButton();
            
            // 4. 发现返回按钮
            elements.backButton = this.discoverBackButton();
            
            // 5. 保存发现的元素
            this.saveDiscoveredElements('search_page', elements);
            
        } catch (e) {
            utils.log("发现搜索页面元素时出错: " + e.toString());
        }

        return elements;
    }

    /**
     * 发现笔记详情页的所有关键元素
     */
    discoverNoteDetailElements() {
        utils.log("=== 开始发现笔记详情页元素 ===");
        
        const elements = {
            commentButton: null,
            likeButton: null,
            shareButton: null,
            backButton: null,
            commentList: null,
            commentInput: null,
            sendButton: null
        };

        try {
            // 1. 发现评论按钮
            elements.commentButton = this.discoverCommentButton();
            
            // 2. 发现点赞按钮
            elements.likeButton = this.discoverLikeButton();
            
            // 3. 发现分享按钮
            elements.shareButton = this.discoverShareButton();
            
            // 4. 发现返回按钮
            elements.backButton = this.discoverBackButton();
            
            // 5. 发现评论相关元素
            elements.commentList = this.discoverCommentList();
            elements.commentInput = this.discoverCommentInput();
            elements.sendButton = this.discoverSendButton();
            
            // 6. 保存发现的元素
            this.saveDiscoveredElements('note_detail', elements);
            
        } catch (e) {
            utils.log("发现笔记详情页元素时出错: " + e.toString());
        }

        return elements;
    }

    /**
     * 发现搜索框
     */
    discoverSearchBox() {
        utils.log("正在发现搜索框...");
        
        const strategies = [
            // 策略1: 查找包含"搜索"提示的输入框
            () => className("android.widget.EditText").textContains("搜索").findOne(2000),
            () => className("android.widget.EditText").hintContains("搜索").findOne(2000),
            
            // 策略2: 查找常见的搜索框ID模式
            () => idContains("search").className("android.widget.EditText").findOne(2000),
            () => idContains("edit").className("android.widget.EditText").findOne(2000),
            () => idContains("input").className("android.widget.EditText").findOne(2000),
            
            // 策略3: 查找页面上的第一个输入框
            () => className("android.widget.EditText").findOne(2000),
            
            // 策略4: 查找可编辑的文本元素
            () => editable(true).findOne(2000)
        ];

        return this.tryStrategies("搜索框", strategies);
    }

    /**
     * 发现搜索按钮
     */
    discoverSearchButton() {
        utils.log("正在发现搜索按钮...");
        
        const strategies = [
            // 策略1: 文本为"搜索"的按钮
            () => text("搜索").clickable(true).findOne(2000),
            () => desc("搜索").clickable(true).findOne(2000),
            
            // 策略2: 包含搜索相关ID的元素
            () => idContains("search").clickable(true).findOne(2000),
            () => idContains("confirm").clickable(true).findOne(2000),
            () => idContains("submit").clickable(true).findOne(2000),
            
            // 策略3: 搜索框右侧的可点击元素
            () => this.findElementRightOf(this.discoverSearchBox()),
            
            // 策略4: 页面右上角的可点击元素
            () => this.findTopRightClickableElement()
        ];

        return this.tryStrategies("搜索按钮", strategies);
    }

    /**
     * 发现筛选按钮
     */
    discoverFilterButton() {
        utils.log("正在发现筛选按钮...");
        
        const strategies = [
            () => text("筛选").clickable(true).findOne(2000),
            () => desc("筛选").clickable(true).findOne(2000),
            () => idContains("filter").clickable(true).findOne(2000),
            () => textContains("筛选").clickable(true).findOne(2000)
        ];

        return this.tryStrategies("筛选按钮", strategies);
    }

    /**
     * 发现返回按钮
     */
    discoverBackButton() {
        utils.log("正在发现返回按钮...");
        
        const strategies = [
            () => desc("返回").clickable(true).findOne(2000),
            () => desc("向上导航").clickable(true).findOne(2000),
            () => desc("Navigate up").clickable(true).findOne(2000),
            () => idContains("back").clickable(true).findOne(2000),
            () => idContains("nav").clickable(true).findOne(2000),
            () => this.findTopLeftClickableElement()
        ];

        return this.tryStrategies("返回按钮", strategies);
    }

    /**
     * 发现评论按钮
     */
    discoverCommentButton() {
        utils.log("正在发现评论按钮...");
        
        const strategies = [
            () => text("评论").clickable(true).findOne(2000),
            () => desc("评论").clickable(true).findOne(2000),
            () => textContains("评论").clickable(true).findOne(2000),
            () => descContains("评论").clickable(true).findOne(2000),
            () => idContains("comment").clickable(true).findOne(2000)
        ];

        return this.tryStrategies("评论按钮", strategies);
    }

    /**
     * 发现点赞按钮
     */
    discoverLikeButton() {
        utils.log("正在发现点赞按钮...");
        
        const strategies = [
            () => desc("点赞").clickable(true).findOne(2000),
            () => desc("赞").clickable(true).findOne(2000),
            () => descContains("点赞").clickable(true).findOne(2000),
            () => idContains("like").clickable(true).findOne(2000),
            () => idContains("praise").clickable(true).findOne(2000)
        ];

        return this.tryStrategies("点赞按钮", strategies);
    }

    /**
     * 发现分享按钮
     */
    discoverShareButton() {
        utils.log("正在发现分享按钮...");
        
        const strategies = [
            () => desc("分享").clickable(true).findOne(2000),
            () => text("分享").clickable(true).findOne(2000),
            () => descContains("分享").clickable(true).findOne(2000),
            () => idContains("share").clickable(true).findOne(2000)
        ];

        return this.tryStrategies("分享按钮", strategies);
    }

    /**
     * 发现评论列表
     */
    discoverCommentList() {
        utils.log("正在发现评论列表...");
        
        const strategies = [
            () => className("androidx.recyclerview.widget.RecyclerView").findOne(2000),
            () => className("android.widget.ListView").findOne(2000),
            () => idContains("comment").className("androidx.recyclerview.widget.RecyclerView").findOne(2000),
            () => idContains("list").findOne(2000)
        ];

        return this.tryStrategies("评论列表", strategies);
    }

    /**
     * 发现评论输入框
     */
    discoverCommentInput() {
        utils.log("正在发现评论输入框...");
        
        const strategies = [
            () => className("android.widget.EditText").textContains("评论").findOne(2000),
            () => className("android.widget.EditText").hintContains("评论").findOne(2000),
            () => className("android.widget.EditText").textContains("说点什么").findOne(2000),
            () => idContains("comment").className("android.widget.EditText").findOne(2000),
            () => idContains("input").className("android.widget.EditText").findOne(2000)
        ];

        return this.tryStrategies("评论输入框", strategies);
    }

    /**
     * 发现发送按钮
     */
    discoverSendButton() {
        utils.log("正在发现发送按钮...");
        
        const strategies = [
            () => text("发送").clickable(true).findOne(2000),
            () => text("发布").clickable(true).findOne(2000),
            () => desc("发送").clickable(true).findOne(2000),
            () => desc("发布").clickable(true).findOne(2000),
            () => idContains("send").clickable(true).findOne(2000),
            () => idContains("submit").clickable(true).findOne(2000)
        ];

        return this.tryStrategies("发送按钮", strategies);
    }

    /**
     * 尝试多种策略查找元素
     */
    tryStrategies(elementName, strategies) {
        for (let i = 0; i < strategies.length; i++) {
            try {
                const element = strategies[i]();
                if (element) {
                    const info = this.getElementInfo(element);
                    utils.log(`✅ 发现${elementName}: ${info}`);
                    return {
                        element: element,
                        info: info,
                        strategy: i
                    };
                }
            } catch (e) {
                utils.log(`策略${i}查找${elementName}失败: ${e.toString()}`);
            }
        }
        
        utils.log(`❌ 未能发现${elementName}`);
        return null;
    }

    /**
     * 获取元素详细信息
     */
    getElementInfo(element) {
        return `ID:${element.id()} Text:"${element.text()}" Desc:"${element.desc()}" Class:${element.className()} Bounds:${element.bounds()}`;
    }

    /**
     * 查找指定元素右侧的可点击元素
     */
    findElementRightOf(baseElement) {
        if (!baseElement || !baseElement.element) return null;
        
        const baseBounds = baseElement.element.bounds();
        const rightX = baseBounds.right + 10;
        const centerY = baseBounds.centerY();
        
        const rightElements = clickable(true).visibleToUser(true).filter(w => {
            const bounds = w.bounds();
            return bounds.left >= rightX && 
                   Math.abs(bounds.centerY() - centerY) < 50 &&
                   bounds.width() > 20 && bounds.height() > 20;
        });
        
        return rightElements.length > 0 ? rightElements[0] : null;
    }

    /**
     * 查找右上角的可点击元素
     */
    findTopRightClickableElement() {
        const screenWidth = device.width;
        const topRightElements = clickable(true).visibleToUser(true).filter(w => {
            const bounds = w.bounds();
            return bounds.right > screenWidth * 0.7 && 
                   bounds.top < device.height * 0.3 &&
                   bounds.width() > 20 && bounds.height() > 20;
        });
        
        return topRightElements.length > 0 ? topRightElements[0] : null;
    }

    /**
     * 查找左上角的可点击元素
     */
    findTopLeftClickableElement() {
        const topLeftElements = clickable(true).visibleToUser(true).filter(w => {
            const bounds = w.bounds();
            return bounds.left < device.width * 0.3 && 
                   bounds.top < device.height * 0.3 &&
                   bounds.width() > 20 && bounds.height() > 20;
        });
        
        return topLeftElements.length > 0 ? topLeftElements[0] : null;
    }

    /**
     * 保存发现的元素到文件
     */
    saveDiscoveredElements(pageName, elements) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `discovered_elements_${pageName}_${timestamp}.json`;
            
            const data = {
                timestamp: new Date().toISOString(),
                pageName: pageName,
                deviceInfo: {
                    width: device.width,
                    height: device.height,
                    package: currentPackage(),
                    activity: currentActivity()
                },
                elements: {}
            };
            
            // 转换元素信息为可保存的格式
            for (const [key, value] of Object.entries(elements)) {
                if (value && value.element) {
                    data.elements[key] = {
                        id: value.element.id(),
                        text: value.element.text(),
                        desc: value.element.desc(),
                        className: value.element.className(),
                        bounds: value.element.bounds().toString(),
                        strategy: value.strategy,
                        info: value.info
                    };
                } else {
                    data.elements[key] = null;
                }
            }
            
            files.write(filename, JSON.stringify(data, null, 2));
            utils.log(`✅ 元素发现结果已保存到: ${filename}`);
            
        } catch (e) {
            utils.log(`保存元素发现结果失败: ${e.toString()}`);
        }
    }

    /**
     * 生成新的选择器配置
     */
    generateSelectorConfig(elements) {
        const config = {};
        
        for (const [key, value] of Object.entries(elements)) {
            if (value && value.element) {
                config[key] = {
                    id: value.element.id(),
                    text: value.element.text(),
                    desc: value.element.desc(),
                    className: value.element.className()
                };
            }
        }
        
        return config;
    }
}

module.exports = ElementDiscovery;
