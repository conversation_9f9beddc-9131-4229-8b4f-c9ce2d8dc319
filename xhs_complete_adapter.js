/**
 * 小红书完整业务流程适配工具
 * 覆盖搜索、笔记列表、笔记详情、评论、AI生成、分享链接等完整流程
 */

// 引入工具模块
var utils = require('./utils.js');

// 检查小红书app界面
function checkXhsApp() {
    utils.log("正在检查小红书app界面...");

    var hasXhsElements = false;
    var foundElements = [];

    try {
        if (textContains("小红书").exists()) {
            hasXhsElements = true;
            foundElements.push("小红书文本");
        }
        if (textContains("发现").exists()) {
            hasXhsElements = true;
            foundElements.push("发现标签");
        }
        if (textContains("关注").exists()) {
            hasXhsElements = true;
            foundElements.push("关注标签");
        }
        if (textContains("搜索").exists()) {
            hasXhsElements = true;
            foundElements.push("搜索元素");
        }

        utils.log("找到的界面元素: " + foundElements.join(", "));

    } catch (e) {
        utils.log("检查界面元素时出错: " + e.toString());
    }

    if (!hasXhsElements) {
        var result = dialogs.confirm("提示",
            "未检测到明确的小红书界面元素\n\n" +
            "找到的元素: " + foundElements.join(", ") + "\n\n" +
            "建议：\n" +
            "1. 确保小红书app已打开并在前台\n" +
            "2. 确保Auto.js有无障碍服务权限\n" +
            "3. 尝试在小红书的主页或搜索页运行\n\n" +
            "是否继续运行适配工具？"
        );

        if (!result) {
            utils.log("用户取消运行");
            exit();
        } else {
            utils.log("用户选择继续运行");
        }
    } else {
        utils.log("✅ 检测到小红书界面元素: " + foundElements.join(", "));
    }
}

/**
 * 1. 发现搜索页面元素
 */
function discoverSearchElements() {
    utils.log("=== 开始发现搜索页面元素 ===");

    var result = {
        searchBox: null,
        searchButton: null,
        filterButton: null,
        backButton: null
    };

    // 搜索框
    utils.log("正在查找搜索框...");
    var searchBoxStrategies = [
        function () { return className("android.widget.EditText").textContains("搜索").findOne(2000); },
        function () { return className("android.widget.EditText").hintContains("搜索").findOne(2000); },
        function () { return idContains("search").className("android.widget.EditText").findOne(2000); },
        function () { return className("android.widget.EditText").findOne(2000); }
    ];
    result.searchBox = tryFindElement("搜索框", searchBoxStrategies);

    // 搜索按钮
    utils.log("正在查找搜索按钮...");
    var searchButtonStrategies = [
        function () { return text("搜索").clickable(true).findOne(2000); },
        function () { return desc("搜索").clickable(true).findOne(2000); },
        function () { return idContains("search").clickable(true).findOne(2000); },
        function () { return idContains("confirm").clickable(true).findOne(2000); }
    ];
    result.searchButton = tryFindElement("搜索按钮", searchButtonStrategies);

    // 筛选按钮
    utils.log("正在查找筛选按钮...");
    var filterButtonStrategies = [
        function () { return text("筛选").clickable(true).findOne(2000); },
        function () { return desc("筛选").clickable(true).findOne(2000); },
        function () { return id("com.xingin.xhs:id/ch9").text("筛选").findOne(2000); },
        function () { return id("com.xingin.xhs:id/hpy").text("筛选").findOne(2000); },
        function () { return idContains("filter").clickable(true).findOne(2000); }
    ];
    result.filterButton = tryFindElement("筛选按钮", filterButtonStrategies);

    // 返回按钮
    utils.log("正在查找返回按钮...");
    var backButtonStrategies = [
        function () { return desc("返回").clickable(true).findOne(2000); },
        function () { return desc("向上导航").clickable(true).findOne(2000); },
        function () { return id("com.xingin.xhs:id/a2q").desc("返回").findOne(2000); },
        function () { return idContains("back").clickable(true).findOne(2000); }
    ];
    result.backButton = tryFindElement("返回按钮", backButtonStrategies);

    saveResults("search", result);
    return result;
}

/**
 * 2. 发现搜索结果页面元素
 */
function discoverSearchResultElements() {
    utils.log("=== 开始发现搜索结果页面元素 ===");

    var result = {
        noteList: null,
        noteItems: null,
        noteCardTitle: null,
        noteCardAuthor: null,
        loadMoreIndicator: null,
        noMoreIndicator: null
    };

    // 笔记列表容器
    utils.log("正在查找笔记列表容器...");
    var noteListStrategies = [
        function () { return className("androidx.recyclerview.widget.RecyclerView").findOne(2000); },
        function () { return className("android.widget.ListView").findOne(2000); },
        function () { return idContains("list").findOne(2000); },
        function () { return idContains("recycler").findOne(2000); }
    ];
    result.noteList = tryFindElement("笔记列表容器", noteListStrategies);

    // 笔记条目
    utils.log("正在查找笔记条目...");
    var noteItemStrategies = [
        function () { return id("com.xingin.xhs:id/hpx").findOne(2000); }, // 已知的笔记容器ID
        function () { return idContains("hpx").findOne(2000); },
        function () { return className("android.widget.FrameLayout").clickable(true).findOne(2000); },
        function () { return className("android.view.ViewGroup").clickable(true).findOne(2000); }
    ];
    result.noteItems = tryFindElement("笔记条目", noteItemStrategies);

    // 笔记卡片标题
    utils.log("正在查找笔记卡片标题...");
    var noteCardTitleStrategies = [
        function () { return id("com.xingin.xhs:id/g_q").findOne(2000); },
        function () { return idContains("g_q").findOne(2000); },
        function () { return className("android.widget.TextView").findOne(2000); }
    ];
    result.noteCardTitle = tryFindElement("笔记卡片标题", noteCardTitleStrategies);

    // 笔记卡片作者
    utils.log("正在查找笔记卡片作者...");
    var noteCardAuthorStrategies = [
        function () { return id("com.xingin.xhs:id/zb").findOne(2000); },
        function () { return idContains("zb").findOne(2000); },
        function () { return className("android.widget.TextView").findOne(2000); }
    ];
    result.noteCardAuthor = tryFindElement("笔记卡片作者", noteCardAuthorStrategies);

    // 加载更多指示器
    utils.log("正在查找加载指示器...");
    var loadMoreStrategies = [
        function () { return textContains("加载").findOne(2000); },
        function () { return textContains("更多").findOne(2000); },
        function () { return text("无更多内容").findOne(2000); }
    ];
    result.loadMoreIndicator = tryFindElement("加载指示器", loadMoreStrategies);

    saveResults("search_result", result);
    return result;
}

/**
 * 3. 发现笔记详情页元素（图文笔记）
 */
function discoverImageTextNoteElements() {
    utils.log("=== 开始发现图文笔记详情页元素 ===");

    var result = {
        // 基本信息
        authorAvatar: null,
        authorNickname: null,
        noteTitle: null,
        noteContent: null,

        // 操作按钮
        likeButton: null,
        collectButton: null,
        commentButton: null,
        shareButton: null,

        // 笔记类型标识
        noteTypeIndicator: null,

        // 分享相关
        topContainer: null,
        moreOperateButton: null
    };

    // 作者头像
    utils.log("正在查找图文笔记作者头像...");
    var authorAvatarStrategies = [
        function () { return id("com.xingin.xhs:id/a0r").className("android.view.FrameLayout").findOne(2000); },
        function () { return idContains("a0r").findOne(2000); }
    ];
    result.authorAvatar = tryFindElement("图文笔记作者头像", authorAvatarStrategies);

    // 作者昵称
    utils.log("正在查找图文笔记作者昵称...");
    var authorNicknameStrategies = [
        function () { return id("com.xingin.xhs:id/nickNameTV").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("nickNameTV").findOne(2000); }
    ];
    result.authorNickname = tryFindElement("图文笔记作者昵称", authorNicknameStrategies);

    // 笔记标题
    utils.log("正在查找图文笔记标题...");
    var noteTitleStrategies = [
        function () { return id("com.xingin.xhs:id/g_s").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("g_s").findOne(2000); }
    ];
    result.noteTitle = tryFindElement("图文笔记标题", noteTitleStrategies);

    // 笔记内容
    utils.log("正在查找图文笔记内容...");
    var noteContentStrategies = [
        function () { return id("com.xingin.xhs:id/drg").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("drg").findOne(2000); }
    ];
    result.noteContent = tryFindElement("图文笔记内容", noteContentStrategies);

    // 点赞按钮
    utils.log("正在查找图文笔记点赞按钮...");
    var likeButtonStrategies = [
        function () { return id("com.xingin.xhs:id/g9w").className("android.widget.ImageView").findOne(2000); },
        function () { return idContains("g9w").findOne(2000); },
        function () { return desc("点赞").clickable(true).findOne(2000); }
    ];
    result.likeButton = tryFindElement("图文笔记点赞按钮", likeButtonStrategies);

    // 收藏按钮
    utils.log("正在查找图文笔记收藏按钮...");
    var collectButtonStrategies = [
        function () { return id("com.xingin.xhs:id/g88").className("android.widget.Button").findOne(2000); },
        function () { return idContains("g88").findOne(2000); },
        function () { return desc("收藏").clickable(true).findOne(2000); }
    ];
    result.collectButton = tryFindElement("图文笔记收藏按钮", collectButtonStrategies);

    // 评论按钮
    utils.log("正在查找图文笔记评论按钮...");
    var commentButtonStrategies = [
        function () { return id("com.xingin.xhs:id/dwu").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("dwu").findOne(2000); },
        function () { return text("评论").clickable(true).findOne(2000); }
    ];
    result.commentButton = tryFindElement("图文笔记评论按钮", commentButtonStrategies);

    // 笔记类型指示器（图文笔记特有）
    utils.log("正在查找图文笔记类型指示器...");
    var noteTypeStrategies = [
        function () { return id("com.xingin.xhs:id/gn_").findOne(2000); }
    ];
    result.noteTypeIndicator = tryFindElement("图文笔记类型指示器", noteTypeStrategies);

    // 分享相关 - 顶部容器
    utils.log("正在查找图文笔记顶部容器...");
    var topContainerStrategies = [
        function () { return id("com.xingin.xhs:id/iw2").findOne(2000); },
        function () { return idContains("iw2").findOne(2000); }
    ];
    result.topContainer = tryFindElement("图文笔记顶部容器", topContainerStrategies);

    // 分享相关 - 更多操作按钮
    utils.log("正在查找图文笔记更多操作按钮...");
    var moreOperateStrategies = [
        function () { return id("com.xingin.xhs:id/moreOperateIV").findOne(2000); },
        function () { return idContains("moreOperateIV").findOne(2000); }
    ];
    result.moreOperateButton = tryFindElement("图文笔记更多操作按钮", moreOperateStrategies);

    saveResults("image_text_note", result);
    return result;
}

/**
 * 4. 发现视频笔记详情页元素
 */
function discoverVideoNoteElements() {
    utils.log("=== 开始发现视频笔记详情页元素 ===");

    var result = {
        // 基本信息
        authorAvatar: null,
        authorNickname: null,

        // 操作按钮容器
        wmContainer: null,
        c9yContainer: null,

        // 操作按钮
        likeButton: null,
        commentButton: null,
        shareButton: null,
        collectButton: null,

        // 分享菜单相关
        gg9Container: null,
        shareMenuRecyclerView: null,
        copyLinkButton: null
    };

    // 视频笔记作者头像
    utils.log("正在查找视频笔记作者头像...");
    var authorAvatarStrategies = [
        function () { return id("com.xingin.xhs:id/jx2").className("android.widget.ImageView").findOne(2000); },
        function () { return idContains("jx2").findOne(2000); }
    ];
    result.authorAvatar = tryFindElement("视频笔记作者头像", authorAvatarStrategies);

    // 视频笔记作者昵称
    utils.log("正在查找视频笔记作者昵称...");
    var authorNicknameStrategies = [
        function () { return id("com.xingin.xhs:id/matrixnickNameView").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("matrixnickNameView").findOne(2000); }
    ];
    result.authorNickname = tryFindElement("视频笔记作者昵称", authorNicknameStrategies);

    // 视频笔记容器
    utils.log("正在查找视频笔记容器...");
    var wmContainerStrategies = [
        function () { return id("com.xingin.xhs:id/wm").findOne(2000); },
        function () { return idContains("wm").findOne(2000); }
    ];
    result.wmContainer = tryFindElement("视频笔记容器", wmContainerStrategies);

    // 功能按钮组件
    utils.log("正在查找视频笔记功能按钮组件...");
    var c9yContainerStrategies = [
        function () { return id("com.xingin.xhs:id/c9y").findOne(2000); },
        function () { return idContains("c9y").findOne(2000); }
    ];
    result.c9yContainer = tryFindElement("视频笔记功能按钮组件", c9yContainerStrategies);

    // 分享菜单容器
    utils.log("正在查找视频笔记分享菜单容器...");
    var gg9ContainerStrategies = [
        function () { return id("com.xingin.xhs:id/gg9").findOne(2000); },
        function () { return idContains("gg9").findOne(2000); }
    ];
    result.gg9Container = tryFindElement("视频笔记分享菜单容器", gg9ContainerStrategies);

    // 分享菜单RecyclerView
    utils.log("正在查找分享菜单RecyclerView...");
    var shareMenuStrategies = [
        function () { return className("androidx.recyclerview.widget.RecyclerView").findOne(2000); }
    ];
    result.shareMenuRecyclerView = tryFindElement("分享菜单RecyclerView", shareMenuStrategies);

    // 复制链接按钮
    utils.log("正在查找复制链接按钮...");
    var copyLinkStrategies = [
        function () { return text("复制链接").findOne(2000); },
        function () { return desc("复制链接").findOne(2000); },
        function () { return textContains("复制链接").findOne(2000); },
        function () { return id("com.xingin.xhs:id/hzs").findOne(2000); }
    ];
    result.copyLinkButton = tryFindElement("复制链接按钮", copyLinkStrategies);

    saveResults("video_note", result);
    return result;
}

/**
 * 5. 发现评论系统元素
 */
function discoverCommentSystemElements() {
    utils.log("=== 开始发现评论系统元素 ===");

    var result = {
        // 评论列表
        commentList: null,
        commentContainer: null,

        // 单条评论元素
        commentAuthor: null,
        commentContent: null,
        commentAvatar: null,
        commentLikeButton: null,

        // 评论输入和发布
        commentInput: null,
        sendButton: null,

        // 评论加载状态
        commentCountIndicator: null,
        loadMoreComments: null,
        commentEndIndicator: null,

        // 评论容器
        singleCommentContainer: null,
        replyContainer: null
    };

    // 评论列表
    utils.log("正在查找评论列表...");
    var commentListStrategies = [
        function () { return className("androidx.recyclerview.widget.RecyclerView").findOne(2000); },
        function () { return idContains("comment").className("androidx.recyclerview.widget.RecyclerView").findOne(2000); }
    ];
    result.commentList = tryFindElement("评论列表", commentListStrategies);

    // 评论作者昵称
    utils.log("正在查找评论作者昵称...");
    var commentAuthorStrategies = [
        function () { return id("com.xingin.xhs:id/jmt").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("jmt").findOne(2000); }
    ];
    result.commentAuthor = tryFindElement("评论作者昵称", commentAuthorStrategies);

    // 评论内容
    utils.log("正在查找评论内容...");
    var commentContentStrategies = [
        function () { return id("com.xingin.xhs:id/jfh").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("jfh").findOne(2000); }
    ];
    result.commentContent = tryFindElement("评论内容", commentContentStrategies);

    // 评论者头像
    utils.log("正在查找评论者头像...");
    var commentAvatarStrategies = [
        function () { return id("com.xingin.xhs:id/e_d").className("android.view.View").findOne(2000); },
        function () { return idContains("e_d").findOne(2000); }
    ];
    result.commentAvatar = tryFindElement("评论者头像", commentAvatarStrategies);

    // 评论点赞按钮
    utils.log("正在查找评论点赞按钮...");
    var commentLikeStrategies = [
        function () { return id("com.xingin.xhs:id/f29").className("android.widget.ImageView").findOne(2000); },
        function () { return idContains("f29").findOne(2000); }
    ];
    result.commentLikeButton = tryFindElement("评论点赞按钮", commentLikeStrategies);

    // 评论输入框
    utils.log("正在查找评论输入框...");
    var commentInputStrategies = [
        function () { return className("android.widget.EditText").textContains("评论").findOne(2000); },
        function () { return className("android.widget.EditText").hintContains("评论").findOne(2000); },
        function () { return className("android.widget.EditText").textContains("说点什么").findOne(2000); },
        function () { return idContains("comment").className("android.widget.EditText").findOne(2000); }
    ];
    result.commentInput = tryFindElement("评论输入框", commentInputStrategies);

    // 发送按钮
    utils.log("正在查找发送按钮...");
    var sendButtonStrategies = [
        function () { return text("发送").clickable(true).findOne(2000); },
        function () { return text("发布").clickable(true).findOne(2000); },
        function () { return desc("发送").clickable(true).findOne(2000); },
        function () { return idContains("send").clickable(true).findOne(2000); }
    ];
    result.sendButton = tryFindElement("发送按钮", sendButtonStrategies);

    // 评论数量指示器
    utils.log("正在查找评论数量指示器...");
    var commentCountStrategies = [
        function () { return id("com.xingin.xhs:id/g8b").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("g8b").findOne(2000); },
        function () { return textContains("条评论").findOne(2000); }
    ];
    result.commentCountIndicator = tryFindElement("评论数量指示器", commentCountStrategies);

    // 加载更多评论
    utils.log("正在查找加载更多评论...");
    var loadMoreStrategies = [
        function () { return id("com.xingin.xhs:id/evn").className("android.widget.TextView").findOne(2000); },
        function () { return idContains("evn").findOne(2000); },
        function () { return textContains("更多评论").findOne(2000); }
    ];
    result.loadMoreComments = tryFindElement("加载更多评论", loadMoreStrategies);

    // 评论结束指示器
    utils.log("正在查找评论结束指示器...");
    var commentEndStrategies = [
        function () { return text("- 到底了 -").findOne(2000); },
        function () { return textContains("到底了").findOne(2000); },
        function () { return text("无更多内容").findOne(2000); }
    ];
    result.commentEndIndicator = tryFindElement("评论结束指示器", commentEndStrategies);

    // 单条评论容器
    utils.log("正在查找单条评论容器...");
    var singleCommentStrategies = [
        function () { return id("com.xingin.xhs:id/eud").findOne(2000); },
        function () { return idContains("eud").findOne(2000); }
    ];
    result.singleCommentContainer = tryFindElement("单条评论容器", singleCommentStrategies);

    // 评论内的回复容器
    utils.log("正在查找评论内回复容器...");
    var replyContainerStrategies = [
        function () { return id("com.xingin.xhs:id/ie1").findOne(2000); },
        function () { return idContains("ie1").findOne(2000); }
    ];
    result.replyContainer = tryFindElement("评论内回复容器", replyContainerStrategies);

    saveResults("comment_system", result);
    return result;
}

/**
 * 尝试查找元素
 */
function tryFindElement(elementName, strategies) {
    for (var i = 0; i < strategies.length; i++) {
        try {
            var element = strategies[i]();
            if (element) {
                var info = "ID:" + element.id() + " Text:\"" + element.text() + "\" Desc:\"" + element.desc() + "\" Class:" + element.className();
                utils.log("✅ 找到" + elementName + ": " + info);
                return {
                    element: element,
                    info: info,
                    strategy: i
                };
            }
        } catch (e) {
            utils.log("策略" + i + "查找" + elementName + "失败: " + e.toString());
        }
    }

    utils.log("❌ 未找到" + elementName);
    return null;
}

/**
 * 保存结果
 */
function saveResults(pageName, results) {
    try {
        var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        var filename = "adapter_result_" + pageName + "_" + timestamp + ".txt";

        var content = "=== " + pageName + "页面适配结果 ===\n";
        content += "时间: " + new Date().toISOString() + "\n";
        content += "设备: " + device.width + "x" + device.height + "\n\n";

        for (var key in results) {
            if (results[key] && results[key].element) {
                content += key + ":\n";
                content += "  ID: " + results[key].element.id() + "\n";
                content += "  Text: " + results[key].element.text() + "\n";
                content += "  Desc: " + results[key].element.desc() + "\n";
                content += "  Class: " + results[key].element.className() + "\n";
                content += "  Bounds: " + results[key].element.bounds() + "\n";
                content += "  Strategy: " + results[key].strategy + "\n\n";
            } else {
                content += key + ": 未找到\n\n";
            }
        }

        files.write(filename, content);
        utils.log("✅ 结果已保存到: " + filename);

    } catch (e) {
        utils.log("保存结果失败: " + e.toString());
    }
}

/**
 * 页面诊断功能
 */
function diagnose() {
    utils.log("=== 开始页面诊断 ===");

    try {
        // 基本信息
        utils.log("页面基本信息:");
        utils.log("- 屏幕尺寸: " + device.width + "x" + device.height);

        // 可点击元素
        var clickableElements = clickable(true).visibleToUser(true).find();
        utils.log("- 可点击元素数量: " + clickableElements.length);

        // 输入框
        var editTexts = className("android.widget.EditText").find();
        utils.log("- 输入框数量: " + editTexts.length);

        // 详细信息
        utils.log("\n前10个可点击元素:");
        for (var i = 0; i < Math.min(clickableElements.length, 10); i++) {
            var elem = clickableElements[i];
            var info = "ID:" + elem.id() + " Text:\"" + elem.text() + "\" Desc:\"" + elem.desc() + "\" Class:" + elem.className();
            utils.log("[" + i + "] " + info);
        }

        // 搜索相关元素
        var searchElements = textContains("搜索").find();
        utils.log("\n包含'搜索'的元素:");
        for (var i = 0; i < searchElements.length; i++) {
            var elem = searchElements[i];
            var info = "ID:" + elem.id() + " Text:\"" + elem.text() + "\" Desc:\"" + elem.desc() + "\" Clickable:" + elem.clickable();
            utils.log("[" + i + "] " + info);
        }

        utils.log("=== 页面诊断完成 ===");

    } catch (e) {
        utils.log("页面诊断失败: " + e.toString());
    }
}

/**
 * 完整流程适配
 */
function discoverAllElements() {
    utils.log("=== 开始完整流程适配 ===");

    var allResults = {
        search: null,
        searchResult: null,
        imageTextNote: null,
        videoNote: null,
        commentSystem: null
    };

    try {
        // 1. 搜索页面元素
        utils.log("第1步：发现搜索页面元素");
        allResults.search = discoverSearchElements();

        // 2. 搜索结果页面元素
        utils.log("第2步：发现搜索结果页面元素");
        allResults.searchResult = discoverSearchResultElements();

        // 3. 图文笔记详情页面元素
        utils.log("第3步：发现图文笔记详情页面元素");
        allResults.imageTextNote = discoverImageTextNoteElements();

        // 4. 视频笔记详情页面元素
        utils.log("第4步：发现视频笔记详情页面元素");
        allResults.videoNote = discoverVideoNoteElements();

        // 5. 评论系统相关元素
        utils.log("第5步：发现评论系统相关元素");
        allResults.commentSystem = discoverCommentSystemElements();

        // 6. 生成完整报告
        generateCompleteReport(allResults);

        utils.log("=== 完整流程适配完成 ===");

    } catch (e) {
        utils.log("完整流程适配失败: " + e.toString());
    }

    return allResults;
}

/**
 * 生成完整报告
 */
function generateCompleteReport(allResults) {
    try {
        var timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        var filename = "xhs_complete_adapter_report_" + timestamp + ".txt";

        var content = "=== 小红书完整业务流程适配报告 ===\n";
        content += "生成时间: " + new Date().toISOString() + "\n";
        content += "设备信息: " + device.width + "x" + device.height + "\n\n";

        content += "📋 适配结果概览:\n";
        for (var pageName in allResults) {
            var pageResult = allResults[pageName];
            if (pageResult) {
                var foundCount = 0;
                var totalCount = 0;
                for (var elementName in pageResult) {
                    totalCount++;
                    if (pageResult[elementName] && pageResult[elementName].element) {
                        foundCount++;
                    }
                }
                content += "- " + pageName + ": " + foundCount + "/" + totalCount + " 个元素\n";
            }
        }

        content += "\n🛠️ 代码更新建议:\n\n";

        // 搜索页面建议
        if (allResults.search) {
            content += "1. 更新 xhs_actions.js 中的搜索相关选择器:\n";
            if (allResults.search.searchBox && allResults.search.searchBox.element) {
                content += "   const searchBox = id(\"" + allResults.search.searchBox.element.id() + "\").findOne(5000);\n";
            }
            if (allResults.search.searchButton && allResults.search.searchButton.element) {
                content += "   const searchButton = id(\"" + allResults.search.searchButton.element.id() + "\").findOne(2000);\n";
            }
            if (allResults.search.filterButton && allResults.search.filterButton.element) {
                content += "   const filterButton = id(\"" + allResults.search.filterButton.element.id() + "\").findOne(2000);\n";
            }
            content += "\n";
        }

        // 搜索结果页建议
        if (allResults.searchResult) {
            content += "2. 更新搜索结果页相关选择器:\n";
            if (allResults.searchResult.noteItems && allResults.searchResult.noteItems.element) {
                content += "   const noteItems = id(\"" + allResults.searchResult.noteItems.element.id() + "\").find();\n";
            }
            if (allResults.searchResult.noteCardTitle && allResults.searchResult.noteCardTitle.element) {
                content += "   const noteTitle = id(\"" + allResults.searchResult.noteCardTitle.element.id() + "\").findOne();\n";
            }
            if (allResults.searchResult.noteCardAuthor && allResults.searchResult.noteCardAuthor.element) {
                content += "   const noteAuthor = id(\"" + allResults.searchResult.noteCardAuthor.element.id() + "\").findOne();\n";
            }
            content += "\n";
        }

        // 图文笔记详情页建议
        if (allResults.imageTextNote) {
            content += "3. 更新图文笔记详情页相关选择器:\n";
            if (allResults.imageTextNote.authorNickname && allResults.imageTextNote.authorNickname.element) {
                content += "   const authorNickname = id(\"" + allResults.imageTextNote.authorNickname.element.id() + "\").findOne();\n";
            }
            if (allResults.imageTextNote.noteTitle && allResults.imageTextNote.noteTitle.element) {
                content += "   const noteTitle = id(\"" + allResults.imageTextNote.noteTitle.element.id() + "\").findOne();\n";
            }
            if (allResults.imageTextNote.noteContent && allResults.imageTextNote.noteContent.element) {
                content += "   const noteContent = id(\"" + allResults.imageTextNote.noteContent.element.id() + "\").findOne();\n";
            }
            if (allResults.imageTextNote.commentButton && allResults.imageTextNote.commentButton.element) {
                content += "   const commentButton = id(\"" + allResults.imageTextNote.commentButton.element.id() + "\").findOne();\n";
            }
            content += "\n";
        }

        // 视频笔记详情页建议
        if (allResults.videoNote) {
            content += "4. 更新视频笔记详情页相关选择器:\n";
            if (allResults.videoNote.authorNickname && allResults.videoNote.authorNickname.element) {
                content += "   const videoAuthorNickname = id(\"" + allResults.videoNote.authorNickname.element.id() + "\").findOne();\n";
            }
            if (allResults.videoNote.wmContainer && allResults.videoNote.wmContainer.element) {
                content += "   const wmContainer = id(\"" + allResults.videoNote.wmContainer.element.id() + "\").findOne();\n";
            }
            if (allResults.videoNote.c9yContainer && allResults.videoNote.c9yContainer.element) {
                content += "   const c9yContainer = id(\"" + allResults.videoNote.c9yContainer.element.id() + "\").findOne();\n";
            }
            content += "\n";
        }

        // 评论系统建议
        if (allResults.commentSystem) {
            content += "5. 更新评论系统相关选择器:\n";
            if (allResults.commentSystem.commentAuthor && allResults.commentSystem.commentAuthor.element) {
                content += "   const commentAuthor = id(\"" + allResults.commentSystem.commentAuthor.element.id() + "\").findOne();\n";
            }
            if (allResults.commentSystem.commentContent && allResults.commentSystem.commentContent.element) {
                content += "   const commentContent = id(\"" + allResults.commentSystem.commentContent.element.id() + "\").findOne();\n";
            }
            if (allResults.commentSystem.commentInput && allResults.commentSystem.commentInput.element) {
                content += "   const commentInput = id(\"" + allResults.commentSystem.commentInput.element.id() + "\").findOne();\n";
            }
            content += "\n";
        }

        content += "📄 详细信息请查看各个单独的结果文件。\n";
        content += "\n💡 使用建议:\n";
        content += "1. 根据上述建议更新相应的js文件中的选择器\n";
        content += "2. 重点关注找到元素数量较少的页面，可能需要手动检查\n";
        content += "3. 测试更新后的功能是否正常工作\n";
        content += "4. 如有问题，可重新运行适配工具获取最新信息\n";

        files.write(filename, content);
        utils.log("✅ 完整报告已保存到: " + filename);

    } catch (e) {
        utils.log("生成完整报告失败: " + e.toString());
    }
}

// 主菜单
function main() {
    // 检查小红书app
    checkXhsApp();

    // 显示工具说明
    var showHelp = dialogs.confirm("小红书完整业务流程适配工具",
        "🎯 工具作用：\n" +
        "当小红书更新后，自动发现整个业务流程的新元素ID：\n" +
        "• 搜索页面（搜索框、搜索按钮、筛选按钮）\n" +
        "• 搜索结果页（笔记列表、笔记条目、标题、作者）\n" +
        "• 图文笔记详情页（作者信息、笔记内容、操作按钮、分享链接）\n" +
        "• 视频笔记详情页（作者信息、功能按钮、分享菜单）\n" +
        "• 评论系统（评论列表、评论内容、输入框、发送按钮）\n\n" +
        "📋 使用方法：\n" +
        "1. 先打开小红书app\n" +
        "2. 进入要适配的页面\n" +
        "3. 运行此脚本选择相应功能\n" +
        "4. 查看生成的适配报告和日志文件\n" +
        "5. 根据报告更新代码中的元素选择器\n\n" +
        "是否继续？"
    );

    if (!showHelp) {
        return;
    }

    var choice = dialogs.select("选择功能", [
        "页面诊断 - 查看当前页面所有元素",
        "发现搜索元素 - 搜索页面相关元素",
        "发现搜索结果元素 - 笔记列表相关元素",
        "发现图文笔记元素 - 图文笔记详情页元素",
        "发现视频笔记元素 - 视频笔记详情页元素",
        "发现评论系统元素 - 评论系统相关元素",
        "完整流程适配 - 一次性发现所有元素"
    ]);

    if (choice === -1) {
        return; // 用户取消
    }

    switch (choice) {
        case 0:
            diagnose();
            break;
        case 1:
            discoverSearchElements();
            break;
        case 2:
            discoverSearchResultElements();
            break;
        case 3:
            discoverImageTextNoteElements();
            break;
        case 4:
            discoverVideoNoteElements();
            break;
        case 5:
            discoverCommentSystemElements();
            break;
        case 6:
            discoverAllElements();
            break;
    }

    // 显示完成提示
    dialogs.alert("完成", "操作完成！请查看日志文件和生成的适配报告。");
}

// 启动主菜单
main();
