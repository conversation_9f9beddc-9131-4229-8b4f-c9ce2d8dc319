/**
 * 设备兼容性管理模块
 * 支持多种设备的元素ID配置，自动检测并使用合适的选择器
 */

const utils = require('./utils.js');

/**
 * 多设备元素选择器配置
 */
const DEVICE_SELECTORS = {
    // 搜索相关元素
    SEARCH_BOX: [
        { name: "通用搜索框1", selector: () => className("android.widget.EditText").textContains("搜索").findOne(2000) },
        { name: "通用搜索框2", selector: () => className("android.widget.EditText").hintContains("搜索").findOne(2000) },
        { name: "ID搜索框", selector: () => idContains("search").className("android.widget.EditText").findOne(2000) },
        { name: "第一个输入框", selector: () => className("android.widget.EditText").findOne(2000) }
    ],
    
    SEARCH_BUTTON: [
        { name: "文本搜索按钮", selector: () => text("搜索").clickable(true).findOne(2000) },
        { name: "描述搜索按钮", selector: () => desc("搜索").clickable(true).findOne(2000) },
        { name: "ID搜索按钮", selector: () => idContains("search").clickable(true).findOne(2000) },
        { name: "确认按钮", selector: () => idContains("confirm").clickable(true).findOne(2000) },
        { name: "提交按钮", selector: () => idContains("submit").clickable(true).findOne(2000) }
    ],
    
    FILTER_BUTTON: [
        { name: "筛选按钮文本", selector: () => text("筛选").clickable(true).findOne(2000) },
        { name: "筛选按钮描述", selector: () => desc("筛选").clickable(true).findOne(2000) },
        { name: "已知ID ch9", selector: () => id("com.xingin.xhs:id/ch9").text("筛选").findOne(2000) },
        { name: "已知ID hpy", selector: () => id("com.xingin.xhs:id/hpy").text("筛选").findOne(2000) },
        { name: "ID筛选", selector: () => idContains("filter").clickable(true).findOne(2000) }
    ],
    
    // 笔记卡片相关元素
    NOTE_CARD_ITEM: [
        { name: "已知ID hpx", selector: () => id("com.xingin.xhs:id/hpx").findOne(2000) },
        { name: "ID包含hpx", selector: () => idContains("hpx").findOne(2000) },
        { name: "可点击FrameLayout", selector: () => className("android.widget.FrameLayout").clickable(true).findOne(2000) },
        { name: "可点击ViewGroup", selector: () => className("android.view.ViewGroup").clickable(true).findOne(2000) }
    ],
    
    NOTE_CARD_TITLE: [
        { name: "已知ID g_q", selector: () => id("com.xingin.xhs:id/g_q").findOne(2000) },
        { name: "ID包含g_q", selector: () => idContains("g_q").findOne(2000) },
        { name: "标题TextView", selector: () => className("android.widget.TextView").findOne(2000) }
    ],
    
    NOTE_CARD_AUTHOR: [
        { name: "已知ID zb", selector: () => id("com.xingin.xhs:id/zb").findOne(2000) },
        { name: "ID包含zb", selector: () => idContains("zb").findOne(2000) },
        { name: "作者TextView", selector: () => className("android.widget.TextView").findOne(2000) }
    ],
    
    // 图文笔记详情页元素
    IMAGE_NOTE_AUTHOR_AVATAR: [
        { name: "已知ID a0r", selector: () => id("com.xingin.xhs:id/a0r").className("android.view.FrameLayout").findOne(2000) },
        { name: "ID包含a0r", selector: () => idContains("a0r").findOne(2000) }
    ],
    
    IMAGE_NOTE_AUTHOR_NICKNAME: [
        { name: "已知ID nickNameTV", selector: () => id("com.xingin.xhs:id/nickNameTV").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含nickNameTV", selector: () => idContains("nickNameTV").findOne(2000) }
    ],
    
    IMAGE_NOTE_TITLE: [
        { name: "已知ID g_s", selector: () => id("com.xingin.xhs:id/g_s").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含g_s", selector: () => idContains("g_s").findOne(2000) }
    ],
    
    IMAGE_NOTE_CONTENT: [
        { name: "已知ID drg", selector: () => id("com.xingin.xhs:id/drg").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含drg", selector: () => idContains("drg").findOne(2000) }
    ],
    
    IMAGE_NOTE_LIKE_BUTTON: [
        { name: "已知ID g9w", selector: () => id("com.xingin.xhs:id/g9w").className("android.widget.ImageView").findOne(2000) },
        { name: "ID包含g9w", selector: () => idContains("g9w").findOne(2000) },
        { name: "点赞描述", selector: () => desc("点赞").clickable(true).findOne(2000) }
    ],
    
    IMAGE_NOTE_COLLECT_BUTTON: [
        { name: "已知ID g88", selector: () => id("com.xingin.xhs:id/g88").className("android.widget.Button").findOne(2000) },
        { name: "ID包含g88", selector: () => idContains("g88").findOne(2000) },
        { name: "收藏描述", selector: () => desc("收藏").clickable(true).findOne(2000) }
    ],
    
    IMAGE_NOTE_COMMENT_BUTTON: [
        { name: "已知ID dwu", selector: () => id("com.xingin.xhs:id/dwu").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含dwu", selector: () => idContains("dwu").findOne(2000) },
        { name: "评论文本", selector: () => text("评论").clickable(true).findOne(2000) }
    ],
    
    IMAGE_NOTE_TYPE_INDICATOR: [
        { name: "已知ID gn_", selector: () => id("com.xingin.xhs:id/gn_").findOne(2000) },
        { name: "ID包含gn_", selector: () => idContains("gn_").findOne(2000) }
    ],
    
    // 视频笔记详情页元素
    VIDEO_NOTE_AUTHOR_AVATAR: [
        { name: "已知ID jx2", selector: () => id("com.xingin.xhs:id/jx2").className("android.widget.ImageView").findOne(2000) },
        { name: "ID包含jx2", selector: () => idContains("jx2").findOne(2000) }
    ],
    
    VIDEO_NOTE_AUTHOR_NICKNAME: [
        { name: "已知ID matrixnickNameView", selector: () => id("com.xingin.xhs:id/matrixnickNameView").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含matrixnickNameView", selector: () => idContains("matrixnickNameView").findOne(2000) }
    ],
    
    VIDEO_NOTE_WM_CONTAINER: [
        { name: "已知ID wm", selector: () => id("com.xingin.xhs:id/wm").findOne(2000) },
        { name: "ID包含wm", selector: () => idContains("wm").findOne(2000) }
    ],
    
    VIDEO_NOTE_C9Y_CONTAINER: [
        { name: "已知ID c9y", selector: () => id("com.xingin.xhs:id/c9y").findOne(2000) },
        { name: "ID包含c9y", selector: () => idContains("c9y").findOne(2000) }
    ],
    
    VIDEO_NOTE_GG9_CONTAINER: [
        { name: "已知ID gg9", selector: () => id("com.xingin.xhs:id/gg9").findOne(2000) },
        { name: "ID包含gg9", selector: () => idContains("gg9").findOne(2000) }
    ],
    
    // 评论系统元素
    COMMENT_AUTHOR: [
        { name: "已知ID jmt", selector: () => id("com.xingin.xhs:id/jmt").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含jmt", selector: () => idContains("jmt").findOne(2000) }
    ],
    
    COMMENT_CONTENT: [
        { name: "已知ID jfh", selector: () => id("com.xingin.xhs:id/jfh").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含jfh", selector: () => idContains("jfh").findOne(2000) }
    ],
    
    COMMENT_AVATAR: [
        { name: "已知ID e_d", selector: () => id("com.xingin.xhs:id/e_d").className("android.view.View").findOne(2000) },
        { name: "ID包含e_d", selector: () => idContains("e_d").findOne(2000) }
    ],
    
    COMMENT_LIKE_BUTTON: [
        { name: "已知ID f29", selector: () => id("com.xingin.xhs:id/f29").className("android.widget.ImageView").findOne(2000) },
        { name: "ID包含f29", selector: () => idContains("f29").findOne(2000) }
    ],
    
    COMMENT_COUNT_INDICATOR: [
        { name: "已知ID g8b", selector: () => id("com.xingin.xhs:id/g8b").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含g8b", selector: () => idContains("g8b").findOne(2000) },
        { name: "评论数量文本", selector: () => textContains("条评论").findOne(2000) }
    ],
    
    COMMENT_LOAD_MORE: [
        { name: "已知ID evn", selector: () => id("com.xingin.xhs:id/evn").className("android.widget.TextView").findOne(2000) },
        { name: "ID包含evn", selector: () => idContains("evn").findOne(2000) },
        { name: "更多评论文本", selector: () => textContains("更多评论").findOne(2000) }
    ],
    
    COMMENT_END_INDICATOR: [
        { name: "到底了文本", selector: () => text("- 到底了 -").findOne(2000) },
        { name: "到底了包含", selector: () => textContains("到底了").findOne(2000) },
        { name: "无更多内容", selector: () => text("无更多内容").findOne(2000) }
    ],
    
    SINGLE_COMMENT_CONTAINER: [
        { name: "已知ID eud", selector: () => id("com.xingin.xhs:id/eud").findOne(2000) },
        { name: "ID包含eud", selector: () => idContains("eud").findOne(2000) }
    ],
    
    REPLY_CONTAINER: [
        { name: "已知ID ie1", selector: () => id("com.xingin.xhs:id/ie1").findOne(2000) },
        { name: "ID包含ie1", selector: () => idContains("ie1").findOne(2000) }
    ],
    
    // 分享链接相关元素
    IMAGE_NOTE_TOP_CONTAINER: [
        { name: "已知ID iw2", selector: () => id("com.xingin.xhs:id/iw2").findOne(2000) },
        { name: "ID包含iw2", selector: () => idContains("iw2").findOne(2000) }
    ],
    
    IMAGE_NOTE_MORE_OPERATE_BUTTON: [
        { name: "已知ID moreOperateIV", selector: () => id("com.xingin.xhs:id/moreOperateIV").findOne(2000) },
        { name: "ID包含moreOperateIV", selector: () => idContains("moreOperateIV").findOne(2000) }
    ],
    
    COPY_LINK_BUTTON: [
        { name: "复制链接文本", selector: () => text("复制链接").findOne(2000) },
        { name: "复制链接描述", selector: () => desc("复制链接").findOne(2000) },
        { name: "复制链接包含", selector: () => textContains("复制链接").findOne(2000) },
        { name: "已知ID hzs", selector: () => id("com.xingin.xhs:id/hzs").findOne(2000) }
    ],
    
    // 返回按钮
    BACK_BUTTON: [
        { name: "返回描述", selector: () => desc("返回").clickable(true).findOne(2000) },
        { name: "向上导航", selector: () => desc("向上导航").clickable(true).findOne(2000) },
        { name: "已知ID a2q", selector: () => id("com.xingin.xhs:id/a2q").desc("返回").findOne(2000) },
        { name: "ID包含back", selector: () => idContains("back").clickable(true).findOne(2000) }
    ]
};

/**
 * 智能元素查找器
 */
class SmartElementFinder {
    constructor() {
        this.cache = {}; // 缓存成功的选择器
        this.deviceFingerprint = this.generateDeviceFingerprint();
    }
    
    /**
     * 生成设备指纹
     */
    generateDeviceFingerprint() {
        return device.width + "x" + device.height + "_" + device.brand + "_" + device.model;
    }
    
    /**
     * 智能查找元素
     * @param {string} elementKey - 元素键名
     * @param {number} timeout - 超时时间
     * @returns {Object|null} 找到的元素对象
     */
    findElement(elementKey, timeout = 2000) {
        // 检查缓存
        const cacheKey = this.deviceFingerprint + "_" + elementKey;
        if (this.cache[cacheKey]) {
            try {
                const cachedResult = this.cache[cacheKey].selector();
                if (cachedResult) {
                    utils.log(`SMART_FINDER: 使用缓存策略找到${elementKey}: ${this.cache[cacheKey].name}`);
                    return cachedResult;
                }
            } catch (e) {
                utils.log(`SMART_FINDER: 缓存策略失效，清除缓存: ${e.toString()}`);
                delete this.cache[cacheKey];
            }
        }
        
        // 获取选择器列表
        const selectors = DEVICE_SELECTORS[elementKey];
        if (!selectors) {
            utils.log(`SMART_FINDER: 未找到${elementKey}的选择器配置`);
            return null;
        }
        
        // 尝试各种选择器
        for (let i = 0; i < selectors.length; i++) {
            try {
                const element = selectors[i].selector();
                if (element) {
                    utils.log(`SMART_FINDER: ✅ 找到${elementKey}: ${selectors[i].name}`);
                    
                    // 缓存成功的选择器
                    this.cache[cacheKey] = selectors[i];
                    
                    return element;
                }
            } catch (e) {
                utils.log(`SMART_FINDER: 策略${i}(${selectors[i].name})失败: ${e.toString()}`);
            }
        }
        
        utils.log(`SMART_FINDER: ❌ 未找到${elementKey}`);
        return null;
    }
    
    /**
     * 批量查找元素
     * @param {Array} elementKeys - 元素键名数组
     * @returns {Object} 查找结果对象
     */
    findElements(elementKeys) {
        const results = {};
        for (const key of elementKeys) {
            results[key] = this.findElement(key);
        }
        return results;
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache = {};
        utils.log("SMART_FINDER: 缓存已清除");
    }
    
    /**
     * 获取缓存统计
     */
    getCacheStats() {
        const stats = {
            deviceFingerprint: this.deviceFingerprint,
            cachedElements: Object.keys(this.cache).length,
            cacheDetails: {}
        };
        
        for (const [key, value] of Object.entries(this.cache)) {
            const elementKey = key.replace(this.deviceFingerprint + "_", "");
            stats.cacheDetails[elementKey] = value.name;
        }
        
        return stats;
    }
}

// 创建全局实例
const smartFinder = new SmartElementFinder();

module.exports = {
    SmartElementFinder,
    DEVICE_SELECTORS,
    smartFinder
};
